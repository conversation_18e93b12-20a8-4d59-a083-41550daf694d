---
type: "manual"
---

# 组件使用示例

本文档提供金题库项目中主要组件的详细使用示例和最佳实践。

## 做题相关组件

### 答题卡组件 (answer-sheet.vue)

答题卡组件用于显示考试或练习中的题目列表，支持题目状态显示和快速跳转。

#### 基础用法

```vue
<template>
  <view>
    <!-- 答题卡按钮 -->
    <button @click="showAnswerSheet = true">显示答题卡</button>
    
    <!-- 答题卡组件 -->
    <answer-sheet 
      v-model="showAnswerSheet"
      :questions="questionList"
      @change="handleQuestionChange"
    />
  </view>
</template>

<script>
import answerSheet from '@/modules/jintiku/components/makeQuestion/answer-sheet.vue'

export default {
  components: {
    answerSheet
  },
  
  data() {
    return {
      showAnswerSheet: false,
      currentQuestionIndex: 0,
      questionList: [
        {
          id: 1,
          user_option: 'A',  // 用户选择的答案
          stem_list: [{
            selected: ['A'],  // 用户选择
            answer: ['A']     // 正确答案
          }],
          doubt: false  // 是否标疑
        },
        {
          id: 2,
          user_option: '',
          stem_list: [{
            selected: [],
            answer: ['B']
          }],
          doubt: true
        }
        // 更多题目...
      ]
    }
  },
  
  methods: {
    handleQuestionChange(index) {
      this.currentQuestionIndex = index
      console.log('跳转到题目:', index + 1)
      // 这里可以添加题目切换逻辑
    }
  }
}
</script>
```

#### 题目状态说明

- **已做**: `user_option` 不为空或 `stem_list[0].selected` 有值
- **未做**: 没有用户选择
- **标疑**: `doubt` 为 `true`

### 底部工具栏组件 (bottom-utils.vue)

底部工具栏提供做题过程中的常用功能，如查看解析、答题卡、收藏、纠错等。

#### 基础用法

```vue
<template>
  <view>
    <!-- 题目内容区域 -->
    <view class="question-content">
      <!-- 题目显示组件 -->
    </view>
    
    <!-- 底部工具栏 -->
    <bottom-utils 
      :current="currentIndex"
      :lists="questionList"
      :utils="toolbarUtils"
      :isnextChapter="showNextChapter"
      @nextChapter="handleNextChapter"
    />
  </view>
</template>

<script>
import bottomUtils from '@/modules/jintiku/components/makeQuestion/bottom-utils.vue'

export default {
  components: {
    bottomUtils
  },
  
  data() {
    return {
      currentIndex: 0,
      questionList: [],
      showNextChapter: false,
      // 配置显示的工具按钮
      toolbarUtils: [
        'lookResolution',  // 查看解析
        'answerSheet',     // 答题卡
        'collect',         // 收藏
        'errorCorrection'  // 纠错
      ]
    }
  },
  
  methods: {
    handleNextChapter() {
      console.log('进入下一章节')
      // 处理下一章节逻辑
    }
  }
}
</script>
```

#### 工具按钮配置

- `lookResolution`: 查看解析按钮
- `answerSheet`: 答题卡按钮
- `errorAnswerSheet`: 错题答题卡
- `collect`: 收藏按钮
- `errorCorrection`: 纠错按钮

### 题目轮播组件 (examination-question-swiper.vue)

题目轮播组件用于展示题目内容，支持左右滑动切换题目。

#### 基础用法

```vue
<template>
  <examination-question-swiper
    :lists="questionList"
    :current="currentIndex"
    :showAnalysis="showAnalysis"
    @change="handleQuestionChange"
    @answer="handleAnswer"
  />
</template>

<script>
import examinationQuestionSwiper from '@/modules/jintiku/components/makeQuestion/examination-question-swiper-new.vue'

export default {
  components: {
    examinationQuestionSwiper
  },
  
  data() {
    return {
      currentIndex: 0,
      showAnalysis: false,
      questionList: [
        {
          id: '1',
          type: '1',  // 题型
          stem_list: [{
            content: '<p>这是题目内容</p>',
            option: ['A. 选项A', 'B. 选项B', 'C. 选项C', 'D. 选项D'],
            answer: ['A'],
            selected: [],
            multiple: false  // 是否多选
          }],
          parse: '<p>这是解析内容</p>'
        }
      ]
    }
  },
  
  methods: {
    handleQuestionChange(index) {
      this.currentIndex = index
    },
    
    handleAnswer(answerData) {
      console.log('用户答题:', answerData)
      // 处理答题逻辑
    }
  }
}
</script>
```

## 通用业务组件

### 表单项组件 (form-item.vue)

统一的表单项组件，提供标签和内容的布局。

#### 基础用法

```vue
<template>
  <view>
    <!-- 左右布局 -->
    <form-item label="用户名" labelPosition="left">
      <input 
        v-model="username" 
        placeholder="请输入用户名"
        class="form-input"
      />
    </form-item>
    
    <!-- 上下布局 -->
    <form-item label="个人简介" labelPosition="top">
      <textarea 
        v-model="bio" 
        placeholder="请输入个人简介"
        class="form-textarea"
      />
    </form-item>
    
    <!-- 带箭头图标 -->
    <form-item label="选择城市" :isPushIcon="true" @click="selectCity">
      <text>{{ selectedCity || '请选择城市' }}</text>
    </form-item>
    
    <!-- 右对齐值 -->
    <form-item label="手机号" valuePos="right">
      <text>{{ phoneNumber }}</text>
    </form-item>
  </view>
</template>

<script>
import formItem from '@/modules/jintiku/components/commen/form-item.vue'

export default {
  components: {
    formItem
  },
  
  data() {
    return {
      username: '',
      bio: '',
      selectedCity: '',
      phoneNumber: '138****8888'
    }
  },
  
  methods: {
    selectCity() {
      // 打开城市选择器
      console.log('选择城市')
    }
  }
}
</script>

<style lang="scss" scoped>
.form-input, .form-textarea {
  width: 100%;
  border: none;
  outline: none;
  font-size: 28rpx;
}

.form-textarea {
  min-height: 120rpx;
  resize: none;
}
</style>
```

### 导航栏组件 (navbar.vue)

自定义导航栏组件，支持返回按钮和标题显示。

#### 基础用法

```vue
<template>
  <view>
    <!-- 基础导航栏 -->
    <navbar title="页面标题" />
    
    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 内容区域 -->
    </view>
  </view>
</template>

<script>
import navbar from '@/modules/jintiku/components/commen/navbar.vue'

export default {
  components: {
    navbar
  }
}
</script>

<style lang="scss" scoped>
.page-content {
  padding: 20rpx;
}
</style>
```

### 倒计时组件 (countdown.vue)

考试倒计时组件，支持时间格式化显示和倒计时结束回调。

#### 基础用法

```vue
<template>
  <view>
    <!-- 考试倒计时 -->
    <countdown 
      :time="examTime"
      @finish="handleTimeUp"
      @update="handleTimeUpdate"
    />
    
    <!-- 倒计时显示 -->
    <view class="time-display">
      剩余时间: {{ formatTime(remainingTime) }}
    </view>
  </view>
</template>

<script>
import countdown from '@/modules/jintiku/components/commen/countdown.vue'

export default {
  components: {
    countdown
  },
  
  data() {
    return {
      examTime: 3600, // 60分钟
      remainingTime: 3600
    }
  },
  
  methods: {
    handleTimeUp() {
      console.log('考试时间到')
      uni.showModal({
        title: '提示',
        content: '考试时间已到，系统将自动交卷',
        showCancel: false,
        success: () => {
          // 自动交卷逻辑
          this.submitExam()
        }
      })
    },
    
    handleTimeUpdate(time) {
      this.remainingTime = time
    },
    
    formatTime(seconds) {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60
      
      if (hours > 0) {
        return `${hours}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`
      }
      return `${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`
    },
    
    submitExam() {
      // 交卷逻辑
      console.log('提交考试')
    }
  }
}
</script>

<style lang="scss" scoped>
.time-display {
  text-align: center;
  font-size: 32rpx;
  color: #ff4757;
  margin: 20rpx 0;
}
</style>
```

## 选择器组件

### 专业选择组件 (select-major.vue)

专业选择弹窗组件，支持专业列表展示和选择。

#### 基础用法

```vue
<template>
  <view>
    <!-- 触发按钮 -->
    <view class="major-selector" @click="showMajorSelector">
      <text>{{ majorName || '请选择专业' }}</text>
      <uni-icons type="arrowright" size="16" color="#999" />
    </view>
    
    <!-- 专业选择组件 -->
    <select-major 
      v-model="majorId"
      :show.sync="showMajor"
      :major_name.sync="majorName"
      @input="handleMajorChange"
    />
  </view>
</template>

<script>
import selectMajor from '@/modules/jintiku/components/select-major.vue'

export default {
  components: {
    selectMajor
  },
  
  data() {
    return {
      majorId: '',
      majorName: '',
      showMajor: false
    }
  },
  
  methods: {
    showMajorSelector() {
      this.showMajor = true
    },
    
    handleMajorChange(majorId) {
      console.log('选择的专业ID:', majorId)
      // 保存专业选择
      uni.setStorageSync('__xingyun_major__', {
        major_id: majorId,
        major_name: this.majorName
      })
      
      // 刷新相关数据
      this.refreshData()
    },
    
    refreshData() {
      // 根据专业刷新数据
      console.log('刷新专业相关数据')
    }
  }
}
</script>

<style lang="scss" scoped>
.major-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #fff;
  border-radius: 8rpx;
  border: 1px solid #e5e5e5;
}
</style>
```

### 选择器外壳组件 (picker-shell.vue)

通用的弹窗选择器容器，可以包装各种选择器内容。

#### 基础用法

```vue
<template>
  <picker-shell 
    :value="showPicker" 
    title="请选择"
    @input="handlePickerChange"
  >
    <!-- 选择器内容 -->
    <view class="picker-content">
      <view 
        v-for="item in options" 
        :key="item.id"
        class="picker-item"
        @click="selectItem(item)"
      >
        {{ item.name }}
      </view>
    </view>
  </picker-shell>
</template>

<script>
import pickerShell from '@/modules/jintiku/components/commen/picker-shell.vue'

export default {
  components: {
    pickerShell
  },
  
  data() {
    return {
      showPicker: false,
      options: [
        { id: 1, name: '选项1' },
        { id: 2, name: '选项2' },
        { id: 3, name: '选项3' }
      ]
    }
  },
  
  methods: {
    handlePickerChange(show) {
      this.showPicker = show
    },
    
    selectItem(item) {
      console.log('选择了:', item)
      this.showPicker = false
      // 处理选择逻辑
    }
  }
}
</script>

<style lang="scss" scoped>
.picker-content {
  max-height: 600rpx;
  overflow-y: auto;
}

.picker-item {
  padding: 30rpx 40rpx;
  border-bottom: 1px solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:active {
    background-color: #f5f5f5;
  }
}
</style>
```

## 最佳实践

### 1. 组件通信

```vue
<!-- 父组件 -->
<template>
  <child-component 
    :prop-data="parentData"
    @child-event="handleChildEvent"
  />
</template>

<script>
export default {
  data() {
    return {
      parentData: 'parent data'
    }
  },
  
  methods: {
    handleChildEvent(data) {
      console.log('收到子组件事件:', data)
    }
  }
}
</script>
```

### 2. 组件复用

```vue
<!-- 可复用的列表项组件 -->
<template>
  <view class="list-item" @click="handleClick">
    <image :src="item.icon" class="item-icon" />
    <view class="item-content">
      <text class="item-title">{{ item.title }}</text>
      <text class="item-desc">{{ item.description }}</text>
    </view>
    <uni-icons type="arrowright" size="16" color="#999" />
  </view>
</template>

<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  
  methods: {
    handleClick() {
      this.$emit('click', this.item)
    }
  }
}
</script>
```

### 3. 错误处理

```vue
<template>
  <view>
    <view v-if="loading">加载中...</view>
    <view v-else-if="error">{{ error }}</view>
    <view v-else>
      <!-- 正常内容 -->
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      error: null,
      data: null
    }
  },
  
  async created() {
    await this.fetchData()
  },
  
  methods: {
    async fetchData() {
      try {
        this.loading = true
        this.error = null
        this.data = await api.getData()
      } catch (error) {
        this.error = '数据加载失败'
        console.error(error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
```
