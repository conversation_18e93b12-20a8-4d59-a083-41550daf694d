---
type: "manual"
---

# 金题库开发文档索引

欢迎使用金题库 UniApp 项目开发文档！本文档将帮助您快速了解项目结构、开发规范和使用方法。

## 📚 文档目录

### 🏠 [项目概述](./README.md)
- 项目基本信息和技术栈
- 项目结构说明
- 快速开始指南
- 环境配置要求

### 📄 [页面结构文档](./pages.md)
- TabBar 导航配置
- 主包页面详解
- 分包页面说明
- 页面路由规则

### 🧩 [组件使用文档](./components.md)
- 全局通用组件
- 业务专用组件
- 组件使用示例
- 组件开发规范

### 🔌 [API 接口文档](./api.md)
- 接口配置说明
- 全局接口列表
- 业务模块接口
- 请求封装使用

### 🛠️ [开发规范和使用指南](./development-guide.md)
- 开发环境搭建
- 编码规范
- Git 工作流
- 构建部署流程
- 调试指南
- 性能优化
- 常见问题解决

## 🚀 快速导航

### 新手入门
1. [环境搭建](./development-guide.md#开发环境搭建)
2. [项目结构](./README.md#项目结构)
3. [运行项目](./README.md#快速开始)

### 开发指南
1. [编码规范](./development-guide.md#编码规范)
2. [组件开发](./components.md#组件使用规范)
3. [API 调用](./api.md#请求封装使用)
4. [页面开发](./pages.md#页面导航规则)

### 部署上线
1. [构建配置](./development-guide.md#构建部署)
2. [环境配置](./development-guide.md#环境配置详解)
3. [性能优化](./development-guide.md#性能优化)

## 📋 核心功能模块

### 🎯 题库练习
- **章节练习**: 按知识点分章节练习
- **真题闯关**: 游戏化闯关答题体验
- **智能测评**: AI 智能分析和推荐
- **模考大赛**: 真实考试环境模拟

### 📚 学习管理
- **错题本**: 错题收集和复习
- **试题收藏**: 重点题目收藏管理
- **学习进度**: 学习数据统计分析
- **成绩报告**: 详细的成绩分析报告

### 👤 用户系统
- **登录注册**: 多种登录方式支持
- **个人中心**: 用户信息管理
- **专业选择**: 学习专业设置
- **设置中心**: 应用个性化设置

### 💰 商业功能
- **商品购买**: 课程和题库商品
- **订单管理**: 订单查看和管理
- **支付集成**: 微信支付等支付方式

## 🔧 技术架构

### 前端技术栈
- **框架**: Vue.js 2.6.11 + UniApp
- **状态管理**: Vuex 3.2.0
- **UI 组件**: uView UI 2.0.31 + Vant 2.12.54
- **网络请求**: flyio
- **构建工具**: Vue CLI 4.5.0

### 支持平台
- 微信小程序 (主要平台)
- H5 Web 应用
- iOS/Android App
- 其他小程序平台

### 开发工具
- **IDE**: HBuilderX (推荐) / VS Code
- **调试**: 微信开发者工具
- **版本控制**: Git
- **代码规范**: ESLint + Prettier

## 📖 使用说明

### 开发流程
1. **需求分析** → 确定功能需求和技术方案
2. **环境搭建** → 配置开发环境和依赖
3. **功能开发** → 按模块进行功能开发
4. **测试调试** → 多平台测试和调试
5. **代码审查** → 代码质量检查和优化
6. **部署上线** → 构建和部署到各平台

### 开发规范
- 遵循 Vue.js 官方风格指南
- 使用 TypeScript 类型检查（可选）
- 组件化开发，提高代码复用性
- 统一的错误处理和日志记录
- 完善的单元测试和集成测试

### 代码质量
- ESLint 代码检查
- Prettier 代码格式化
- Git Hooks 提交检查
- 持续集成和部署

## 🤝 贡献指南

### 参与开发
1. Fork 项目到个人仓库
2. 创建功能分支进行开发
3. 提交代码并创建 Pull Request
4. 代码审查通过后合并到主分支

### 问题反馈
- 通过 Issue 提交 Bug 报告
- 提供详细的问题描述和复现步骤
- 附上相关的错误日志和截图

### 文档贡献
- 完善现有文档内容
- 添加新的使用示例
- 翻译文档到其他语言

## 📞 技术支持

### 常见问题
- 查看 [开发指南](./development-guide.md#常见问题解决)
- 搜索已有的 Issue
- 查阅 UniApp 官方文档

### 联系方式
- 项目维护者：[联系信息]
- 技术交流群：[群号]
- 邮箱支持：[邮箱地址]

## 📝 更新日志

### v1.4.14 (当前版本)
- 优化用户体验
- 修复已知问题
- 新增功能特性

### 历史版本
- 查看 [CHANGELOG.md](./CHANGELOG.md) 了解详细更新记录

## 📄 许可证

本项目采用 [MIT License](../LICENSE) 许可证。

---

**注意**: 本文档会持续更新，请关注最新版本。如有疑问或建议，欢迎提交 Issue 或 Pull Request。
