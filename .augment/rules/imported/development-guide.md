---
type: "manual"
---

# 开发规范和使用指南

## 开发环境搭建

### 环境要求

- **Node.js**: >= 12.0.0
- **npm**: >= 6.0.0 或 **yarn**: >= 1.0.0
- **HBuilderX**: 推荐使用官方 IDE
- **微信开发者工具**: 小程序开发必需

### 安装步骤

1. **克隆项目**
```bash
git clone [项目地址]
cd wb_prdt
```

2. **安装依赖**
```bash
npm install
# 或
yarn install
```

3. **环境配置**
```bash
# 复制环境配置文件
cp .env.example .env.development
```

4. **启动开发服务**
```bash
# H5 开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin
```

## 项目结构规范

### 目录命名规范

- **文件夹**: kebab-case (小写字母，连字符分隔)
- **组件文件**: PascalCase.vue
- **页面文件**: kebab-case.vue
- **工具文件**: camelCase.js

### 文件组织原则

```
src/
├── api/                    # 全局 API
├── components/             # 全局组件
├── modules/               # 业务模块
│   └── jintiku/          # 金题库模块
│       ├── api/          # 模块 API
│       ├── components/   # 模块组件
│       ├── pages/        # 页面文件
│       ├── store/        # 状态管理
│       ├── utils/        # 工具函数
│       └── mixin/        # 混入
├── static/               # 静态资源
├── store/                # 全局状态
├── utlis/                # 全局工具
└── App.vue               # 应用入口
```

## 编码规范

### Vue 组件规范

#### 1. 组件命名
```javascript
// 组件文件名：PascalCase
// UserProfile.vue
// QuestionCard.vue

// 组件注册：PascalCase
export default {
  name: 'UserProfile',
  components: {
    QuestionCard
  }
}
```

#### 2. Props 定义
```javascript
export default {
  props: {
    // 基础类型检查
    title: String,
    // 多类型检查
    value: [String, Number],
    // 必填且有默认值
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    // 对象默认值
    config: {
      type: Object,
      default: () => ({})
    }
  }
}
```

#### 3. 事件命名
```javascript
// 使用 kebab-case
this.$emit('update-value', newValue)
this.$emit('item-click', item)

// 模板中
<component @update-value="handleUpdate" @item-click="handleClick" />
```

#### 4. 组件结构顺序
```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
export default {
  name: 'ComponentName',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
/* 样式内容 */
</style>
```

### JavaScript 规范

#### 1. 变量命名
```javascript
// 常量：UPPER_SNAKE_CASE
const API_BASE_URL = 'https://api.example.com'

// 变量和函数：camelCase
const userName = 'john'
const getUserInfo = () => {}

// 类名：PascalCase
class UserService {}
```

#### 2. 函数定义
```javascript
// 优先使用箭头函数
const handleClick = () => {
  // 处理逻辑
}

// 异步函数
const fetchData = async () => {
  try {
    const response = await api.getData()
    return response.data
  } catch (error) {
    console.error('获取数据失败:', error)
    throw error
  }
}
```

#### 3. 对象和数组
```javascript
// 对象解构
const { name, age } = user
const { data: responseData } = response

// 数组解构
const [first, second] = list

// 扩展运算符
const newUser = { ...user, age: 25 }
const newList = [...list, newItem]
```

### CSS 规范

#### 1. 类名命名
```scss
// BEM 命名规范
.question-card {
  &__header {
    // 元素样式
  }
  
  &__title {
    // 元素样式
  }
  
  &--active {
    // 修饰符样式
  }
}
```

#### 2. 样式组织
```scss
// 变量定义
$primary-color: #2E68FF;
$border-radius: 8rpx;

// 混入定义
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 组件样式
.component {
  @include flex-center;
  color: $primary-color;
  border-radius: $border-radius;
}
```

## Git 工作流规范

### 分支管理

```
master          # 主分支，生产环境
├── develop     # 开发分支
├── feature/*   # 功能分支
├── hotfix/*    # 热修复分支
└── release/*   # 发布分支
```

### 提交信息规范

```bash
# 格式：<type>(<scope>): <subject>

# 类型说明
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动

# 示例
feat(user): 添加用户登录功能
fix(question): 修复题目显示错误
docs(api): 更新API文档
```

### 工作流程

1. **创建功能分支**
```bash
git checkout -b feature/user-login
```

2. **开发和提交**
```bash
git add .
git commit -m "feat(user): 添加登录表单组件"
```

3. **推送和合并**
```bash
git push origin feature/user-login
# 创建 Pull Request
```

## 构建部署

### 开发环境

```bash
# H5 开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin

# 其他平台
npm run dev:mp-alipay    # 支付宝小程序
npm run dev:app-plus     # App
```

### 测试环境

```bash
# 测试环境构建
npm run build:h5-test
npm run build:mp-weixin-test
```

### 生产环境

```bash
# 生产环境构建
npm run build:h5
npm run build:mp-weixin
npm run build:app-plus
```

### 部署流程

1. **代码检查**
```bash
npm run lint
npm run test
```

2. **构建项目**
```bash
npm run build:h5
```

3. **部署到服务器**
```bash
# 根据具体部署方案执行
```

## 调试指南

### 开发工具

#### 1. H5 调试
- 使用浏览器开发者工具
- Vue DevTools 插件
- 网络请求监控

#### 2. 微信小程序调试
- 微信开发者工具
- 真机调试
- 性能监控

#### 3. App 调试
- HBuilderX 真机运行
- 原生调试工具

### 常见问题

#### 1. 跨平台兼容性
```javascript
// 平台判断
// #ifdef H5
// H5 特有代码
// #endif

// #ifdef MP-WEIXIN
// 微信小程序特有代码
// #endif

// #ifdef APP-PLUS
// App 特有代码
// #endif
```

#### 2. 样式适配
```scss
// rpx 单位自动转换
.container {
  width: 750rpx; // 等于屏幕宽度
  height: 100rpx; // 自动适配
}

// 安全区域适配
.safe-area {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
```

#### 3. API 兼容性
```javascript
// 使用 uni API
uni.request({
  url: 'https://api.example.com',
  success: (res) => {
    console.log(res.data)
  }
})

// 条件编译
// #ifdef MP-WEIXIN
wx.login({
  success: (res) => {
    console.log(res.code)
  }
})
// #endif
```

## 性能优化

### 1. 代码分割
```javascript
// 路由懒加载
const UserProfile = () => import('@/pages/user/profile')

// 组件懒加载
components: {
  AsyncComponent: () => import('@/components/AsyncComponent')
}
```

### 2. 图片优化
```vue
<template>
  <!-- 使用 webp 格式 -->
  <image 
    :src="imageUrl" 
    mode="aspectFit"
    lazy-load
    @load="handleImageLoad"
    @error="handleImageError"
  />
</template>
```

### 3. 请求优化
```javascript
// 请求缓存
const cache = new Map()

const fetchWithCache = async (url) => {
  if (cache.has(url)) {
    return cache.get(url)
  }
  
  const data = await api.get(url)
  cache.set(url, data)
  return data
}
```

## 测试规范

### 单元测试
```javascript
// 组件测试
import { mount } from '@vue/test-utils'
import UserCard from '@/components/UserCard.vue'

describe('UserCard', () => {
  it('renders user name correctly', () => {
    const wrapper = mount(UserCard, {
      propsData: {
        user: { name: 'John Doe' }
      }
    })
    expect(wrapper.text()).toContain('John Doe')
  })
})
```

### 接口测试
```javascript
// API 测试
import { getUserInfo } from '@/api/user'

describe('User API', () => {
  it('should fetch user info', async () => {
    const userInfo = await getUserInfo({ id: 1 })
    expect(userInfo).toHaveProperty('name')
    expect(userInfo).toHaveProperty('email')
  })
})
```

## 代码质量

### ESLint 配置
```javascript
// .eslintrc.js
module.exports = {
  extends: [
    '@vue/standard'
  ],
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off'
  }
}
```

### Prettier 配置
```javascript
// .prettierrc.js
module.exports = {
  semi: false,
  singleQuote: true,
  trailingComma: 'none'
}
```

## 安全规范

### 1. 数据验证
```javascript
// 输入验证
const validateInput = (input) => {
  if (!input || typeof input !== 'string') {
    throw new Error('Invalid input')
  }
  return input.trim()
}
```

### 2. 敏感信息处理
```javascript
// 不要在代码中硬编码敏感信息
// 使用环境变量
const apiKey = process.env.VUE_APP_API_KEY
```

### 3. XSS 防护
```vue
<template>
  <!-- 使用 v-text 而不是 v-html -->
  <div v-text="userInput"></div>

  <!-- 必须使用 v-html 时要确保内容安全 -->
  <div v-html="sanitizedHtml"></div>
</template>
```

## 常用工具函数

### 1. 时间处理
```javascript
// src/modules/jintiku/utils/index.js

// 时间格式化
export const formatTime = (timestamp, format = 'YYYY-MM-DD HH:mm:ss') => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

// 倒计时转换
export const transformTime = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`
  }
  return `${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`
}
```

### 2. 数据处理
```javascript
// 深拷贝
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

// 防抖函数
export const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 节流函数
export const throttle = (func, limit) => {
  let inThrottle
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}
```

### 3. 存储工具
```javascript
// 本地存储封装
export const storage = {
  set(key, value) {
    try {
      uni.setStorageSync(key, JSON.stringify(value))
    } catch (error) {
      console.error('Storage set error:', error)
    }
  },

  get(key, defaultValue = null) {
    try {
      const value = uni.getStorageSync(key)
      return value ? JSON.parse(value) : defaultValue
    } catch (error) {
      console.error('Storage get error:', error)
      return defaultValue
    }
  },

  remove(key) {
    try {
      uni.removeStorageSync(key)
    } catch (error) {
      console.error('Storage remove error:', error)
    }
  },

  clear() {
    try {
      uni.clearStorageSync()
    } catch (error) {
      console.error('Storage clear error:', error)
    }
  }
}
```

## 状态管理使用

### Vuex Store 结构
```javascript
// src/modules/jintiku/store/index.js
export default {
  state: {
    token: uni.getStorageSync('__xingyun_token__') || '',
    userinfo: uni.getStorageSync('__xingyun_userinfo__') || {},
    majorInfo: uni.getStorageSync('__xingyun_major__') || {}
  },

  mutations: {
    setToken(state, token) {
      state.token = token
      uni.setStorageSync('__xingyun_token__', token)
    },

    setUserinfo(state, userinfo) {
      state.userinfo = userinfo
      uni.setStorageSync('__xingyun_userinfo__', userinfo)
    }
  },

  actions: {
    async login({ commit }, loginData) {
      try {
        const response = await Login(loginData)
        commit('setToken', response.data.token)
        commit('setUserinfo', response.data.userinfo)
        return response
      } catch (error) {
        throw error
      }
    }
  },

  getters: {
    isLogin: state => !!state.token,
    userName: state => state.userinfo.name || '未登录'
  }
}
```

### 在组件中使用
```vue
<template>
  <view>
    <text>{{ userName }}</text>
    <button @click="handleLogin" v-if="!isLogin">登录</button>
  </view>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  computed: {
    ...mapState('jintiku', ['userinfo']),
    ...mapGetters('jintiku', ['isLogin', 'userName'])
  },

  methods: {
    ...mapActions('jintiku', ['login']),

    async handleLogin() {
      try {
        await this.login({
          username: 'test',
          password: '123456'
        })
        uni.showToast({ title: '登录成功' })
      } catch (error) {
        uni.showToast({ title: '登录失败', icon: 'none' })
      }
    }
  }
}
</script>
```

## 混入使用

### 通用混入
```javascript
// src/modules/jintiku/mixin/index.js
export default {
  data() {
    return {
      loading: false
    }
  },

  methods: {
    // 显示加载
    showLoading(title = '加载中...') {
      this.loading = true
      uni.showLoading({ title })
    },

    // 隐藏加载
    hideLoading() {
      this.loading = false
      uni.hideLoading()
    },

    // 显示提示
    showToast(title, icon = 'none') {
      uni.showToast({ title, icon })
    },

    // 页面跳转
    navigateTo(url) {
      uni.navigateTo({ url })
    },

    // 返回上一页
    navigateBack(delta = 1) {
      uni.navigateBack({ delta })
    }
  }
}
```

### 在组件中使用混入
```vue
<script>
import mixin from '@/modules/jintiku/mixin'

export default {
  mixins: [mixin],

  async onLoad() {
    this.showLoading('数据加载中...')
    try {
      await this.fetchData()
    } catch (error) {
      this.showToast('加载失败')
    } finally {
      this.hideLoading()
    }
  }
}
</script>
```

## 环境配置详解

### 环境变量配置
```bash
# .env.development
NODE_ENV = development
VUE_APP_BASE_API = '/api'
VUE_APP_BASICKEY = 's2b2c2b'
VUE_APP_BASICVALUE = 'qOEffNlL1H5Qh!wA@ekN%ry7v521Nz97'
VUE_APP_PLATFORMID = '409974729504527968'
VUE_APP_MERCHANTID = '385164383640140890'
VUE_APP_BRANDID = '389806835466550453'
```

### 配置文件使用
```javascript
// src/modules/jintiku/config.js
export const app_id = 'wxa8ca85794337e175'
export const shelf_platform_id = '480130129201204499'
export const version = '1.4.14'

// 订阅消息配置
export const requestSubscribeMessage = {
  login: 's1j0HFiAisliLGd_XuLYP1aPE-OAQ2sAJvH0tYwZrSE',
  examination: 'PS9wWvqZkhqF_o-5ov6KqGfx67yDUJ0AetG00gnX4Ic',
  goods: '1PdJWyEZvp5JTmC8kU62gFBLxq6lhd3h_i8iJo9O2eg',
  share: '8PR9F9xSl4q22YRFJPdMy8o2GLNLDNIkRrX8XBXZTaA'
}
```

## 常见问题解决

### 1. 跨域问题
```javascript
// vue.config.js
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'https://xingyundev.jinyingjie.com',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        }
      }
    }
  }
}
```

### 2. 图片加载失败
```vue
<template>
  <image
    :src="imageSrc"
    @error="handleImageError"
    mode="aspectFit"
  />
</template>

<script>
export default {
  data() {
    return {
      imageSrc: 'https://example.com/image.jpg',
      defaultImage: '/static/images/default.png'
    }
  },

  methods: {
    handleImageError() {
      this.imageSrc = this.defaultImage
    }
  }
}
</script>
```

### 3. 网络请求超时
```javascript
// 请求超时处理
const request = (options) => {
  return new Promise((resolve, reject) => {
    const timer = setTimeout(() => {
      reject(new Error('请求超时'))
    }, 10000)

    uni.request({
      ...options,
      success: (res) => {
        clearTimeout(timer)
        resolve(res)
      },
      fail: (err) => {
        clearTimeout(timer)
        reject(err)
      }
    })
  })
}
```
