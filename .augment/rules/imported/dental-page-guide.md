---
type: "manual"
---

# 口腔执业页面开发指南

## 页面概述

根据提供的截图，我已经创建了一个完整的口腔执业页面，包含以下功能模块：

- 自定义导航栏（显示倒计时）
- 笔试精讲班广告横幅
- 练习功能网格布局
- 章节模考、科目模考、模拟考试列表

## 文件结构

```
src/modules/jintiku/
├── pages/dental/
│   ├── index.vue          # 主页面
│   └── test.vue           # 组件测试页面
├── components/commen/
│   ├── practice-card.vue  # 练习卡片组件
│   ├── exam-item.vue      # 考试项目组件
│   └── section-title.vue  # 章节标题组件
└── static/images/dental/
    └── README.md          # 图片资源说明
```

## 组件说明

### 1. practice-card.vue - 练习卡片组件

**功能**: 展示练习项目，支持图标、标题、副标题和点击跳转

**Props**:
- `icon`: 图标地址
- `title`: 主标题
- `subtitle`: 副标题
- `url`: 跳转链接

**使用示例**:
```vue
<practice-card
  icon="https://via.placeholder.com/80x80/FF6B35/FFFFFF?text=💪"
  title="核心突破5000题"
  subtitle="冲刺必刷 快速提分"
  url="/modules/jintiku/pages/chapterExercise/index?type=core"
  @click="handlePracticeClick"
/>
```

### 2. exam-item.vue - 考试项目组件

**功能**: 展示考试信息，支持进度条、时间显示和操作按钮

**Props**:
- `title`: 考试标题
- `questionCount`: 题目数量
- `duration`: 考试时长
- `difficulty`: 难度等级
- `startTime`: 开始时间（可选）
- `showProgress`: 是否显示进度条
- `currentProgress`: 当前进度
- `totalProgress`: 总进度
- `actionText`: 操作按钮文本
- `isPrimary`: 是否为主要按钮样式

**使用示例**:
```vue
<exam-item
  title="口腔实践技能题库"
  :questionCount="105"
  duration="永久"
  :showProgress="true"
  :currentProgress="0"
  :totalProgress="105"
  actionText="立即刷题"
  @click="handleExamClick"
  @action="handleExamAction"
/>
```

### 3. section-title.vue - 章节标题组件

**功能**: 统一的章节标题样式，带有装饰线条

**Props**:
- `title`: 标题文本

**使用示例**:
```vue
<section-title title="章节模考" />
```

## 页面配置

已在 `src/pages.json` 中添加了页面配置：

```json
{
  "path": "modules/jintiku/pages/dental/index",
  "style": {
    "navigationBarTitleText": "口腔执业",
    "enablePullDownRefresh": true,
    "navigationStyle": "custom"
  }
}
```

## 样式特点

### 1. 设计风格
- 使用渐变色主题（橙色系）
- 圆角卡片设计
- 阴影效果增强层次感
- 响应式网格布局

### 2. 颜色规范
- 主色调：`#FF6B35` 到 `#FF8E53` 渐变
- 背景色：`#f5f6f7`
- 文字色：`#333`（主要）、`#666`（次要）、`#999`（辅助）

### 3. 间距规范
- 页面边距：`32rpx`
- 组件间距：`24rpx`
- 内容间距：`16rpx`
- 小间距：`8rpx`

## 数据结构

### 练习项目数据
```javascript
{
  id: 1,
  icon: 'https://via.placeholder.com/80x80/FF6B35/FFFFFF?text=💪',
  title: '核心突破5000题',
  subtitle: '冲刺必刷 快速提分',
  url: '/modules/jintiku/pages/chapterExercise/index?type=core'
}
```

### 考试项目数据
```javascript
{
  title: '口腔实践技能题库',
  questionCount: 105,
  duration: '永久',
  difficulty: '永久',
  currentProgress: 0,
  totalProgress: 105,
  actionText: '立即刷题'
}
```

## 功能实现

### 1. 导航栏倒计时
- 显示距离考试的剩余天数
- 可以通过接口动态获取

### 2. 广告横幅
- 支持点击跳转到课程详情
- 渐变背景 + 图标装饰

### 3. 练习功能网格
- 2x2 网格布局
- 每个卡片支持独立跳转

### 4. 考试列表
- 支持不同类型的考试展示
- 进度条显示（可选）
- 时间信息显示（可选）

## 测试页面

创建了 `test.vue` 页面用于测试组件功能：

访问路径：`/modules/jintiku/pages/dental/test`

## 使用步骤

### 1. 访问页面
```javascript
uni.navigateTo({
  url: '/modules/jintiku/pages/dental/index'
})
```

### 2. 自定义数据
修改 `data()` 中的数据结构来自定义显示内容：

```javascript
// 修改练习项目
practiceItems: [
  {
    id: 1,
    icon: '你的图标地址',
    title: '你的标题',
    subtitle: '你的副标题',
    url: '你的跳转链接'
  }
  // 更多项目...
]
```

### 3. 接入真实数据
在 `onLoad()` 或 `onShow()` 生命周期中调用 API 获取真实数据：

```javascript
async onLoad() {
  try {
    const practiceData = await getPracticeList()
    this.practiceItems = practiceData
    
    const examData = await getExamList()
    this.chapterExam = examData.chapterExam
    // 更多数据处理...
  } catch (error) {
    console.error('数据加载失败:', error)
  }
}
```

## 扩展建议

### 1. 添加加载状态
```vue
<template>
  <view v-if="loading" class="loading">
    <uni-load-more status="loading" />
  </view>
  <view v-else>
    <!-- 页面内容 -->
  </view>
</template>
```

### 2. 添加错误处理
```vue
<template>
  <view v-if="error" class="error">
    <text>{{ error }}</text>
    <button @click="retry">重试</button>
  </view>
</template>
```

### 3. 添加下拉刷新
```javascript
onPullDownRefresh() {
  this.refreshData().finally(() => {
    uni.stopPullDownRefresh()
  })
}
```

### 4. 添加统计埋点
```javascript
handlePracticeClick(item) {
  // 统计埋点
  this.$xh.track('practice_click', {
    practice_id: item.id,
    practice_title: item.title
  })
  
  // 页面跳转
  if (item.url) {
    uni.navigateTo({ url: item.url })
  }
}
```

## 注意事项

1. **图片资源**: 当前使用占位图片，实际使用时需要替换为真实图片
2. **跳转链接**: 确保所有跳转链接对应的页面已存在
3. **数据格式**: 确保后端返回的数据格式与组件期望的格式一致
4. **平台兼容**: 测试在不同平台（H5、小程序、App）的显示效果
5. **性能优化**: 大列表时考虑使用虚拟滚动或分页加载

## 维护更新

- 新增练习类型：在 `practiceItems` 数组中添加新项目
- 修改样式：在对应组件的 `<style>` 部分调整
- 添加功能：在组件的 `methods` 中添加新方法
- 数据对接：在页面的生命周期方法中调用相应 API
