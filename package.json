{"name": "uniapp_lbr", "version": "0.1.0", "private": true, "scripts": {"start": "node bin/index.js", "serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:mp-weixin-test": "cross-env NODE_ENV=test UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:mp-weixin-pre": "cross-env NODE_ENV=pre UNI_PLATFORM=mp-weixin vue-cli-service uni-build", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "build:quickapp-webview-huawei": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build", "build:quickapp-webview-union": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:h5-test": "cross-env NODE_ENV=devtest UNI_PLATFORM=h5 vue-cli-service uni-serve", "builddev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-build", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize", "dev:mp-weixin-test": "cross-env NODE_ENV=test UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize", "dev:mp-weixin-pre": "cross-env NODE_ENV=pre UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch --minimize", "dev:mp-weixin-prod": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "dev:quickapp-webview-huawei": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build --watch", "dev:quickapp-webview-union": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest -i", "test:h5": "cross-env UNI_PLATFORM=h5 jest -i", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest -i", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest -i", "test:mp-weixin": "cross-env NODE_ENV=test UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "pre:mp-weixin": "cross-env NODE_ENV=pre UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "build:h5-test": "cross-env NODE_ENV=test UNI_PLATFORM=h5 vue-cli-service uni-build", "build:h5-prod": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:h5-pre": "cross-env NODE_ENV=pre UNI_PLATFORM=h5 vue-cli-service uni-build", "build:h5-dev": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-build", "buildtest:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-build"}, "dependencies": {"@dcloudio/uni-app-plus": "^2.0.0-32220210818002", "@dcloudio/uni-h5": "^2.0.0-32220210818002", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-mp-360": "^2.0.0-32220210818002", "@dcloudio/uni-mp-alipay": "^2.0.0-32220210818002", "@dcloudio/uni-mp-baidu": "^2.0.0-32220210818002", "@dcloudio/uni-mp-kuaishou": "^2.0.0-32220210818002", "@dcloudio/uni-mp-qq": "^2.0.0-32220210818002", "@dcloudio/uni-mp-toutiao": "^2.0.0-32220210818002", "@dcloudio/uni-mp-vue": "^2.0.0-32220210818002", "@dcloudio/uni-mp-weixin": "^2.0.0-32220210818002", "@dcloudio/uni-quickapp-native": "^2.0.0-32220210818002", "@dcloudio/uni-quickapp-webview": "^2.0.0-32220210818002", "@dcloudio/uni-stat": "^2.0.0-32220210818002", "@dcloudio/uni-ui": "^1.4.2", "@vue/shared": "^3.0.0", "core-js": "^3.6.5", "echarts": "^5.3.3", "flyio": "^0.6.2", "md5": "^2.3.0", "pdfh5": "^1.4.2", "qrcode": "^1.5.4", "regenerator-runtime": "^0.12.1", "sass-loader": "7.1.0", "terser-webpack-plugin": "4.2.3", "uview-ui": "^2.0.31", "vant": "2.12.54", "vue": "^2.6.11", "vuex": "^3.2.0", "weixin-js-sdk": "^1.6.0", "zg-sdk-uniapp": "^1.0.5"}, "devDependencies": {"@babel/runtime": "~7.12.0", "@dcloudio/types": "*", "@dcloudio/uni-automator": "^2.0.0-32220210818002", "@dcloudio/uni-cli-i18n": "^2.0.1-34720220422002", "@dcloudio/uni-cli-shared": "^2.0.0-32220210818002", "@dcloudio/uni-i18n": "^2.0.0-alpha-33020211130001", "@dcloudio/uni-migration": "^2.0.0-32220210818002", "@dcloudio/uni-template-compiler": "^2.0.0-32220210818002", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.0-32220210818002", "@dcloudio/vue-cli-plugin-uni": "^2.0.0-32220210818002", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.0-32220210818002", "@dcloudio/webpack-uni-mp-loader": "^2.0.0-32220210818002", "@dcloudio/webpack-uni-pages-loader": "^2.0.0-32220210818002", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^7.0.0", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.11.0", "child_process": "^1.0.2", "cross-env": "^7.0.2", "eslint": "^8.9.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.4.1", "husky": "^7.0.4", "jest": "^25.4.0", "less": "^3.9.0", "less-loader": "^5.0.0", "lint-staged": "^12.3.4", "mini-types": "*", "miniprogram-api-typings": "*", "nodemon": "^2.0.15", "postcss-comment": "^2.0.0", "prettier": "^2.5.1", "sass": "^1.38.1", "vue-template-compiler": "^2.6.11"}, "browserslist": ["Android >= 4", "ios >= 8"], "uni-app": {"scripts": {}}}