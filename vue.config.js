const webpack = require('webpack')
const path = require('path')
const TerserPlugin = require('terser-webpack-plugin')
function resolve(dir) {
  return path.join(__dirname, dir)
}
module.exports = {
  transpileDependencies: ['uview-ui', '@dcloudio/uni-ui'],
  configureWebpack: {
    plugins: [
      // new webpack.ProvidePlugin({
      //   // 对库做全局别名处理
      //   localStorage: ['mp-storage', 'localStorage'],
      //   'window.localStorage': ['mp-storage', 'localStorage'],
      // }),
    ],
    optimization: {
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            compress: {
              warnings: false,
              drop_console: false, // 生产环境移除console
              drop_debugger: true // 生产环境移除debugger
            },
            output: {
              // 最紧凑的输出
              beautify: false,
              // 删除所有的注释
              comments: false
            }
          }
        })
      ]
    }
  },
  chainWebpack: config => {
    config.resolve.alias
      .set('@utlis', resolve('/src/utlis'))
      .set('@', resolve('/src'))
      .set('@api', resolve('/src/api'))
      .set('@components', resolve('/src/components'))
    // config.plugin('define').tap(args => {
    //   // 配置全局环境变量 例如：
    //   args[0]['process.env'].VUE_APP_TEST = '"test"'
    //   return args
    // })

    // 修复 Sass Loader 配置问题
    const oneOfs = config.module.rule('scss').oneOfs.store
    oneOfs.forEach(item => {
      item.use('sass-loader').tap(options => {
        if (options.data) {
          options.prependData = options.data
          delete options.data
        }
        return options
      })
    })
  }
}
