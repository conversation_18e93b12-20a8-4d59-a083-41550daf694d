<template>
  <view class="questionPlan">
    <view class="card">
      <view class="time">
        <text>
          考试倒计时
          <text class="red">{{ info.countdown }}</text>
          天
        </text>
      </view>
      <view class="ranking">
        <text>今日排名：</text>
        <text class="red">{{ info.rank }}</text>
      </view>
      <view class="desc">
        <text>连续刷题</text>
        <text class="main-color">{{ info.continuous_day_number }}</text>
        <text>天，</text>
        <text>已刷题</text>
        <text class="red">{{ info.total_day_number }}</text>
        <text>天</text>
      </view>
      <view class="buttons">
        <view
          class="button flex-center"
          @click="
            goDetail(
              `pages/intelligentEvaluation/scheduleReport?evaluation_id=${evaluation_id}`
            )
          "
        >
          进度报告
        </view>
        <view
          class="button flex-center plan"
          @click="goDetail('pages/intelligentEvaluation/ranking')"
        >
          排行榜
        </view>
      </view>
    </view>
    <view class="lists">
      <view class="list">
        <view class="title"> 今日任务 </view>
        <view class="time"> {{ info.brush_question_plan_info.name }} </view>
        <view class="nums">
          <view class="num-box">
            <view class="num-info">
              <view class="num-title main-color">
                {{ info.brush_question_plan_info.question_num }}道
              </view>
              <view class="desc">答题量</view>
            </view>
            <view class="num-info">
              <view class="num-title red">
                {{ info.brush_question_plan_info.correct_number }}
              </view>
              <view class="desc">正确数</view>
            </view>
          </view>
          <view
            class="computed flex-center"
            v-if="info.brush_question_plan_info.status == '3'"
          >
            未开启
          </view>
          <view
            class="computed flex-center"
            v-else
            :class="{ success: info.brush_question_plan_info.status == '0' }"
            @click="
              goDetail(
                `pages/intelligentEvaluation/practise?type=4&evaluation_plan_id=${info.brush_question_plan_info.plan_id}`,
                info.brush_question_plan_info.status,
                info.brush_question_plan_info.plan_id
              )
            "
          >
            {{ getStatusName(info.brush_question_plan_info.status) }}
          </view>
        </view>
      </view>
      <view class="list" v-for="(item, i) in planDetailList" :key="i">
        <view class="time"> {{ item.name }} </view>
        <view class="nums">
          <view class="num-box">
            <view class="num-info">
              <view class="num-title main-color">
                {{ item.question_num }}道
              </view>
              <view class="desc">答题量</view>
            </view>
            <view class="num-info">
              <view class="num-title red"> {{ item.correct_number }} </view>
              <view class="desc">正确数</view>
            </view>
          </view>
          <view class="computed flex-center" v-if="item.status == '3'">
            未开启
          </view>
          <view
            class="computed flex-center"
            :class="{ success: item.status == '0' }"
            @click="
              goDetail(
                `pages/intelligentEvaluation/practise?type=4&evaluation_plan_id=${item.plan_id}`,
                item.status,
                item.plan_id
              )
            "
            v-else
          >
            {{ getStatusName(item.status) }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import { getPlanList } from '../../api/intelligentEvaluation'
export default {
  data() {
    return {
      info: {
        brush_question_plan_info: {
          correct_number: '1', // 答对试题数量
          correct_rate: '答题正确率',
          create_time: 'string',
          id: '刷题计划详情id',
          name: '刷题任务名称',
          plan_id: '刷题任务id',
          practice_record_id: '答题记录id',
          question_num: '', // 应答题数
          receive_gold_coin_status: '金币领取状态(0:可领取 1:已领取)',
          status: '0' // 刷题任务状态 0:去完成 1:去复习 2:去补做 3:未开启
        },
        continuous_day_number: '8', // 连续刷题天数
        countdown: '49', // 考试倒计时天数
        evaluative_lexis: '刷题计划对应的评价词',
        join_number: '1', // 答题正确数
        new_class_id: '金题库专业id',
        participate_number: '50', // 答题量

        rank: '89', // 排名
        remind_status: '是否提醒(1:是 0:否)，因新系统没有，所以可传默认值',
        remind_time: '提醒时间',
        should_day_number: '31', // 应刷天数
        status: '提醒状态(0:不提醒 1:提醒)',
        total_day_number: '3' //已刷题天数
      },
      evaluation_id: '',
      practice_record_id: '',
      plan_id: '',
      planDetailList: [
        {
          correct_number: '2', // 答对试题数量
          correct_rate: '答题正确率',
          create_time: '2023-10-09',
          id: 'xxxxxxxxxx',
          name: '刷题任务名称',
          plan_id: '', // 刷题任务id
          practice_record_id: '答题记录id',
          question_num: '12', // 应答题数
          receive_gold_coin_status: '金币领取状态(0:可领取 1:已领取)',
          status: '3' // 刷题任务状态 0:去完成 1:去复习 2:去补做 3:未开启
        }
      ]
    }
  },
  onLoad(e) {
    this.plan_id = e.plan_id
    this.evaluation_id = e.evaluation_id
    this.practice_record_id = e.practice_record_id
  },
  onShow() {
    this.getList()
  },
  methods: {
    getStatusName(status) {
      let obj = {
        0: '去完成',
        1: '去复习',
        2: '去补做',
        3: '未开启'
      }
      return obj[status] ? obj[status] : '去完成'
    },
    goDetail(url, status = '0', evaluation_plan_id = '') {
      if (status == '1') {
        // 去复习
        this.$xh.push(
          'jintiku',
          `pages/makeQuestion/lookAnalysisQuestion?type=4&evaluation_plan_id=${evaluation_plan_id}`
        )
        return
      }
      this.$xh.push('jintiku', url)
    },
    getList() {
      getPlanList({
        evaluation_id: this.evaluation_id
      }).then(data => {
        this.planDetailList = data.data.planDetailList
        this.info = data.data
      })
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.questionPlan {
  height: 100vh;
  overflow-y: scroll;
  background-color: #f5f5f5;
  padding: 28rpx;
  .card {
    padding: 40rpx 30rpx;
    background-color: #fff;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    .time {
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #333333;
      line-height: 32rpx;
      margin-bottom: 49rpx;
    }
    .ranking {
      font-size: 28rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 800;
      color: #333333;
      line-height: 30rpx;
      text-align: center;
      margin-bottom: 30rpx;
    }
    .desc {
      font-size: 28rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 28rpx;
      margin-bottom: 55rpx;
      text-align: center;
    }
    .buttons {
      display: flex;
      align-items: center;
      justify-content: space-around;
      .button {
        width: 220rpx;
        height: 60rpx;
        font-size: 30rpx;
        border-radius: 30rpx;
        color: #387dfc;
        border: 1px solid #387dfc;
      }
      .plan {
        background-color: #387dfc;
        color: #fff;
      }
    }
  }
  .lists {
    .list {
      padding: 30rpx;
      background-color: #fff;
      border-radius: 20rpx;
      margin-bottom: 20rpx;
      .title {
        height: 60rpx;
        line-height: 60rpx;
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 800;
        text-align: center;
        color: #387dfc;
        margin-bottom: 20rpx;
      }
      .time {
        font-size: 30rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 800;
        color: #333333;
        line-height: 30rpx;
        margin-bottom: 40rpx;
      }
      .nums {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .num-box {
          display: flex;
          align-items: center;
          .num-info {
            margin-right: 50rpx;
            font-size: 26rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
            line-height: 30rpx;
            .num-title {
              margin-bottom: 30rpx;
            }
          }
        }
        .computed {
          width: 150rpx;
          height: 60rpx;
          color: #387dfc;
          border: 1px solid #387dfc;
          font-size: 30rpx;
          border-radius: 30rpx;
        }
        .success {
          color: #fff;
          background-color: #387dfc;
        }
      }
    }
  }
}
</style>
