<template>
  <view class="intelligentEvaluation">
    <view class="title"> 精准测试能力，学习水平定位 </view>
    <view class="scroll">
      <swiper
        class="swiper"
        :indicator-dots="false"
        :autoplay="false"
        :current="current"
        :duration="300"
        :circular="true"
        previous-margin="50rpx"
        next-margin="380rpx"
        @change="swiperChange"
      >
        <swiper-item v-for="(item, index) in lists" :key="index">
          <view class="swiper-item" :class="{ active: index == current }">
            <view
              class="card"
              :class="{
                current: item.state == '1',
                success: item.state == '2'
              }"
            >
              <view class="title">
                <text>{{ item.level_name }}</text>
                <view class="status"></view>
              </view>
              <view class="desc"> {{ item.level_desc }} </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
    <!-- 状态卡 -->
    <view class="statusCard">
      <!--测试还没开启 跳级开启测试 -->
      <view class="skip-card statusCardInfo" v-if="currentObj.state == '0'">
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967484360212c14169674843602244959_zncp.png"
          mode="widthFix"
          class="ai-test"
        />
        <view class="test">
          <text> 高频考题、易错易混题测试，及格过关必测 </text>
        </view>
        <view class="button bt60" @click="skip"></view>
        <view class="desc">
          每阶段测试，答题正确率{{ currentObj.pass_right_rate }}%以上或完成推荐
          刷题计划，可解锁下一等级测评
        </view>
      </view>
      <!-- 已经开启了练习卡片，但是还没有开启任何测评 -->
      <view
        class="not-practice-card statusCardInfo"
        v-if="isShowNotPracticeCard()"
      >
        <image
          src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967484360212c14169674843602244959_zncp.png"
          mode="widthFix"
          class="ai-test"
        />
        <view class="test">
          <text>
            基础知识摸底，
            <text class="red">10</text>
            万学员已完成测评
          </text>
        </view>
        <view
          class="button"
          @click="
            goDetail(
              'pages/intelligentEvaluation/practise?evaluation_id=' +
                currentObj.id
            )
          "
        >
          立即测试
        </view>
        <view class="desc">
          每阶段测试，答题正确率{{ currentObj.pass_right_rate }}%以上或完成推荐
          刷题计划，可解锁下一等级测评
        </view>
      </view>
      <!-- 开启了练习卡片 并且进行了测试 出现了测评报告 -->
      <view class="report-card statusCardInfo" v-if="isShowReport()">
        <view class="reoprt">
          <view class="report-title-info">
            <view class="myhtitle"> 我的测评报告 </view>

            <view class="date">
              <!-- 日期 -->
              {{ currentObj.report_info.create_time }}
            </view>
          </view>
          <view class="status">
            <view class="status-name">
              <!-- 测评评语 -->
              {{ currentObj.report_info.test_report }}
            </view>
            <!-- 去重新测评按钮 -->
            <view
              class="reset-button"
              @click="
                goDetail(
                  'pages/intelligentEvaluation/practise?evaluation_id=' +
                    currentObj.id
                )
              "
            >
            </view>
          </view>
          <view class="line"></view>
          <view class="reprot-desc">
            <text class="text">
              做题正确率
              <text class="active">
                {{ currentObj.report_info.correct_rate }}%
              </text>
              ，掌握知识点
              <text class="active">
                {{ currentObj.report_info.grasp_knowledge_num }}
              </text>
              /{{ currentObj.report_info.knowledge_num }}
            </text>
            <!-- 测评报告按钮 -->
            <view
              class="report-button button"
              @click="
                goDetail(
                  `pages/intelligentEvaluation/report?report_id=${currentObj.report_info.report_id}&evaluation_id=${currentObj.id}`
                )
              "
            >
            </view>
          </view>
        </view>
        <view class="tip">
          <!-- 测试分析和评价词 -->
          {{ currentObj.report_info.report_analyse }}
        </view>
        <!-- 专属刷题计划 -->
        <view class="reoprt quesiton-report" v-if="isShowPlanReportCard()">
          <view class="report-title-info">
            <view class="myhtitle"> 专属刷题计划 </view>
          </view>
          <view class="reprot-desc reprot-desc1">
            <text class="text">
              您需坚持刷题
              <text class="active">
                {{ currentObj.do_question_plan.should_day_number }}
              </text>
              天，已刷题
              <text class="active">
                {{ currentObj.do_question_plan.total_day_number }}
              </text>
              天
            </text>
            <!-- 立即测评按钮 -->
            <view
              class="test-button button"
              @click="
                goDetail(
                  `pages/intelligentEvaluation/questionPlan?plan_id=${currentObj.do_question_plan.evaluation_id}&report_id=${currentObj.report_info.report_id}&evaluation_id=${currentObj.id}`
                )
              "
            >
            </view>
          </view>
          <view class="line"></view>
          <view class="reprot-desc">
            <text class="text">
              已刷
              <text class="active">
                {{ currentObj.do_question_plan.do_question_num }}道题
              </text>
              ，正确率
              <text class="active">
                {{ currentObj.do_question_plan.correct_rate }}%
              </text>
            </text>
            <!-- 刷题报告按钮 -->
            <view
              class="question-report-button button"
              @click="
                goDetail(
                  `pages/intelligentEvaluation/scheduleReport?evaluation_id=${currentObj.id}`
                )
              "
            >
            </view>
          </view>
        </view>
        <view class="button" v-if="isShowPlanButton()" @click="openPlan">
          开启专属刷题计划
        </view>
        <!-- 测评率达标 之后开启下一等级按钮 -->
        <view v-if="isShowPassTestButton()">
          <view class="button" @click="next"> 下一等级测评 </view>
          <!-- <view class="button share"> 炫耀一下成绩 </view> -->
        </view>
      </view>
    </view>
    <view class="no-data" v-if="isNoData">
      <image
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967484360212c14169674843602244959_zncp.png"
        mode="widthFix"
      />
      <text>该专业下暂无任何测评哦！换一个专业试试吧</text>
    </view>
  </view>
</template>
<script>
import { getevaluationlist, setPlan } from '../../api/intelligentEvaluation'
export default {
  data() {
    return {
      current: 0,
      lists: [
        // {
        //   rate: 80,
        //   date: '2020-09-20',
        //   title: '牛逼',
        //   knows: 40,
        //   totalKnows: 100,
        //   desc: '正确率高的很啊 牛逼牛逼',
        //   maxRate: 60, // 过关率
        //   plan: false // 是否开启了专属计划
        // },
      ],
      isNoData: false
    }
  },
  onShow() {
    this.getList()
  },
  methods: {
    skip() {
      this.$xh.Toast('小程序暂不支持跳级哦，请下载金题库App！')
    },
    // 开启专属刷题计划按钮
    openPlan() {
      setPlan({
        evaluation_situation_id: this.currentObj.report_info.report_id
      }).then(data => {
        this.$xh.Toast('开启成功，赶快去刷题吧！')
        this.getList()
      })
    },
    swiperChange(e) {
      this.current = e.detail.current
    },
    goDetail(url) {
      this.$xh.push('jintiku', url)
    },
    getList() {
      getevaluationlist({}).then(data => {
        this.lists = data.data ? data.data : []
        if (!this.lists.length) {
          this.isNoData = true
          return
        }
        if (this.lists[0].state != '2') {
          this.lists[0].state = '1'
        }
        // this.lists[0].state = '1'
        // 计算当前做到哪一个等级了
        // let index = JSON.parse(JSON.stringify(this.lists))
        //   .reverse()
        //   .findIndex(item => item.state == '1')
        // this.current = this.lists.length - index - 1
        let tmpIndex = this.lists.findIndex(item => {
          return item.state == '1'
        })
        if (tmpIndex >= 0) {
          this.current = tmpIndex
        } else {
          this.current = this.lists.length - 1
        }
      })
    },
    isShowNotPracticeCard() {
      if (!this.lists.length) {
        return false
      }
      return (
        this.currentObj.state != '0' && !this.currentObj.report_info.test_report
      )
    },
    isShowReport() {
      if (!this.lists.length) {
        return false
      }
      return (
        this.currentObj.state != '0' && this.currentObj.report_info.test_report
      )
    },
    // 是否显示开启专属计划按钮
    isShowPlanButton() {
      if (!this.lists.length) {
        return false
      }
      // 暂未开启刷题计划 并且 过关率不达标
      return (
        this.currentObj.isHasDoQuestionPlan != '1' &&
        this.currentObj.report_info.correct_rate * 1 <
          this.currentObj.pass_right_rate * 1
      )
    },
    // 是否显示刷题计划的report卡片
    isShowPlanReportCard() {
      if (!this.lists.length) {
        return false
      }
      // 开启了刷题计划 并且测试不达标
      return this.currentObj.isHasDoQuestionPlan == '1'
      // (
      //    && this.currentObj.report_info.correct_rate * 1 < this.currentObj.pass_right_rate * 1
      // )
    },
    // 是否显示下一等级按钮
    isShowPassTestButton() {
      if (!this.lists.length) {
        return false
      }
      // 测评率达标并且不是最后一个卡片
      return (
        this.currentObj.report_info.correct_rate * 1 >=
          this.currentObj.pass_right_rate * 1 &&
        this.lists.length - 1 == this.current
      )
    },
    // 开启下一等级
    next() {
      if (this.current + 1 >= this.lists.length) {
        this.$xh.Toast('太棒了，您已经通过全部的智能测评啦！')
        return false
      }
      this.current += 1
      return true
    }
  },
  computed: {
    currentObj() {
      let result = this.lists.length ? this.lists[this.current] : {}
      uni.setStorageSync('__ai_test__', result)
      return result
    }
  },
  onShareAppMessage() {
    return this.$xh.shareAppMessage()
  }
}
</script>
<style scoped lang="less">
.intelligentEvaluation {
  height: 100vh;
  overflow-y: scroll;
  background-color: #fff;
  .swiper {
    height: 180rpx;
  }
  .title {
    font-size: 32rpx;
    font-family: AppleSystemUIFont;
    color: #333333;
    line-height: 32rpx;
    margin: 20rpx 0;
    color: #999999;
    padding-left: 20rpx;
  }
  .card {
    background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967456248386347169674562483832557_BG.png')
      no-repeat;
    background-size: cover;
    width: 300rpx;
    height: 150rpx;
    padding: 30rpx;
    padding-top: 10rpx;
    padding-right: 20rpx;
    .title {
      display: flex;
      align-items: center;
      padding-left: 0;
      text {
        font-size: 36rpx;
        font-family: AppleSystemUIFont;
        color: #999999;
        line-height: 36rpx;
      }
      .status {
        width: 96rpx;
        height: 40rpx;
        background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169674580513912d7169674580514096843_no-kq.png')
          no-repeat;
        background-size: cover;
        margin-left: 8rpx;
      }
    }
    .desc {
      font-size: 24rpx;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 800;
      color: #999999;
      line-height: 28rpx;
    }
    &.current {
      background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169674729675231c8169674729675441431_current.png')
        no-repeat;
      background-size: cover;
      .title {
        text {
          color: #387dfc;
        }
        .status {
          background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967471971241338169674719712452950_ing.png')
            no-repeat;
          background-size: cover;
        }
      }
      .desc {
        color: #387dfc;
      }
    }
    &.success {
      background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967474333197105169674743332012748_ss.png')
        no-repeat;
      background-size: cover;
      .title {
        text {
          color: #ff9500;
        }
        .status {
          background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169674745476686f5169674745476656233_s.png')
            no-repeat;
          background-size: cover;
        }
      }
      .desc {
        color: #ff9500;
      }
    }
  }
  .statusCard {
    .statusCardInfo {
      margin-top: 110rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      .ai-test {
        width: 497rpx;
        height: 300rpx;
        margin-bottom: 80rpx;
      }
      .test {
        text-align: center;
        margin-bottom: 40rpx;
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 28rpx;
        .red {
          color: #f44141;
        }
      }
      .button {
        width: 600rpx;
        height: 90rpx;
        line-height: 90rpx;
        background: #387dfc;
        border-radius: 45rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        // margin-bottom: 60rpx;
      }
      .desc {
        width: 485rpx;
        text-align: center;
        font-size: 24rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 36rpx;
        margin-top: 20rpx;
      }
    }
    .skip-card {
      .button {
        background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169674991992340a616967499199237222_suo.png')
          no-repeat;
        background-size: cover;
      }
    }
    .report-card {
      background: #f5f5f5;
      padding: 40rpx 30rpx;
      margin-top: 0;
      min-height: calc(100vh - 256rpx);
      justify-content: flex-start;
      .reoprt {
        // height: 343rpx;
        background-color: #fff;
        border-radius: 20rpx;
        width: 100%;
        padding: 50rpx 30rpx;
        .report-title-info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 50rpx;
          .myhtitle {
            font-size: 32rpx;
            font-family: STSongti-SC-Black, STSongti-SC;
            font-weight: 900;
            color: #333333;
            line-height: 30rpx;
          }
          .date {
            width: 180rpx;
            height: 44rpx;
            line-height: 44rpx;
            background: rgba(56, 125, 252, 0.1);
            border-radius: 22rpx 0px 0px 22rpx;
            font-size: 24rpx;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 800;
            color: #387dfc;
            text-align: right;
            position: relative;
            right: -30rpx;
            padding-right: 10rpx;
          }
        }
        .status {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 40rpx;
          .status-name {
            font-size: 48rpx;
            font-family: STSongti-SC-Black, STSongti-SC;
            font-weight: 900;
            color: #387dfc;
            line-height: 48rpx;
          }
          .reset-button {
            width: 170rpx;
            height: 60rpx;
            background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169675127065415fa169675127065471815_reset.png')
              no-repeat;
            background-size: cover;
          }
        }
        .line {
          border-bottom: 1px dashed #387dfc;
          margin-bottom: 49rpx;
        }
        .reprot-desc {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .text {
            font-size: 28rpx;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
            line-height: 28rpx;
            .active {
              color: #387dfc;
            }
          }
          .report-button {
            width: 170rpx;
            height: 60rpx;
            background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1696753336075846f169675333607642596_report.png')
              no-repeat;
            background-size: cover;
          }
          .question-report-button {
            width: 170rpx;
            height: 60rpx;
            background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967584149314871169675841493224352_shauitbaogao.png')
              no-repeat;
            background-size: cover;
          }
        }
      }
      .quesiton-report {
        margin-top: -160rpx;
        margin-bottom: 60rpx;
        .reprot-desc1 {
          margin-bottom: 40rpx;
          .test-button {
            width: 170rpx;
            height: 60rpx;
            background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16967582871525e2a169675828715342439_ljtest.png')
              no-repeat;
            background-size: cover;
          }
        }
      }
      .share {
        background-color: #fff;
        color: #387dfc;
        border: 1px solid #387dfc;
      }
      .tip {
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 42rpx;
        margin-top: 40rpx;
        margin-bottom: 200rpx;
        text-align: center;
      }
    }
  }
  .bt60 {
    margin-bottom: 60rpx;
  }
  .no-data {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    image {
      width: 80%;
      height: auto;
      flex-shrink: 0;
      margin-bottom: 40rpx;
      margin-top: 100rpx;
    }
    text {
      text-align: center;
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 36rpx;
      margin-top: 20rpx;
    }
  }
}
</style>
