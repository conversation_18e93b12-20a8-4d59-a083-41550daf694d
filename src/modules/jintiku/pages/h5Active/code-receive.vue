<template>
  <!--  #ifdef H5  -->
  <view class="code-receive">
    <view class="head"> {{ info.active_title }} </view>
    <view class="time">
      {{ getTime(info.active_start_time) }}至{{ getTime(info.active_end_time) }}
    </view>
    <view class="card">
      <view class="title"> {{ info.exchange_code.name }} </view>
      <view class="lq">
        <view class="flex" v-for="(item, i) in exchange_codes" :key="i">
          <view class="code">
            {{ item.exchange_code ? item.exchange_code : 'FZ***2x' }}
          </view>
          <view
            class="btn active"
            :class="{ share: item.is_share != 1 && item.is_receive != '1' }"
            @click="getCode(item, i)"
          >
            <text v-if="item.is_share == 1 || i == 0">
              {{ item.is_receive == '1' ? '已领取' : '点击领取' }}
            </text>
            <text v-else> 分享领取 </text>
          </view>
        </view>
      </view>
    </view>
    <view class="code-duihuan">
      <view class="title">
        <view class="line"> </view>
        <view class="title"> 兑换码领取相关说明 </view>
      </view>
      <view class="desc">
        <view class="desc-list">
          1.兑换码领取规则：每人最多可领取3个兑换码，第一次分享成功可领取1个兑换码，第二次分享成功可领取第2个兑换码。
        </view>
        <view class="desc-list">
          2.兑换码使用途径：
          a.下载金题库App，在学习-我的-兑换码界面使用并兑换你心仪的商品。
          b.在金题库App-学习-我的模块查看商品。
        </view>
        <view class="desc-list">
          3.兑换码的有效时间：
          领取兑换码后，请在兑换码对应的日期内使用，过期作废，不予退换。
        </view>
        <view class="desc-list"> 4.本活动最终解释权归金英杰所有。 </view>
      </view>
    </view>
    <!-- 分享弹框 -->
    <view class="share-box" v-if="shre">
      <image
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1701168373745143b170116837374514212_arrow.png"
        class="right"
        mode="widthFix"
      ></image>
      <image
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1701168347991209f170116834799270801_zhidaole.png"
        mode="widthFix"
        @click="iKnow"
      ></image>
    </view>
    <!-- 领取成功弹框 -->
    <view class="success-mask flex-center" v-if="show">
      <view class="success-wrapper">
        <view class="head">
          <image
            src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/17007932422278b46170079324222785415_sus.png"
            mode="widthFix"
          />
        </view>
        <view class="gx"> 恭喜您 </view>
        <view class="text">
          您已经成功领取了
          <text>{{ num }}</text>
          个激活码！ 快去兑换心仪的商品吧！
        </view>
        <view class="btn flex-center" @click="buy">马上兑换</view>
        <view class="close flex-center" @click="show = false">x</view>
      </view>
    </view>
  </view>
  <!--  #endif -->
</template>
<script>
//#ifdef H5
import { getCodeDetail, setCode, codeShare, getCodes } from '../../api/h5Active'
export default {
  data() {
    return {
      show: false,
      shre: false,
      id: '',
      info: {},
      exchange_codes: [],
      index: 1
    }
  },
  onLoad(e) {
    this.id = e.id
    this.init()
  },
  show() {},
  computed: {
    num() {
      if (!this.exchange_codes) {
        return 0
      }
      return this.exchange_codes.filter(item => !item.exchange_code).length
    }
  },
  methods: {
    iKnow() {
      this.shre = false
      // 分享
      codeShare({
        id: this.info.id,
        index: this.index
      }).then(data => {
        this.init()
      })
    },
    buy() {
      this.$xh.push('jintiku', 'pages/h5Active/open-app')
    },
    init() {
      getCodeDetail({
        id: this.id,
        identity: 1
      }).then(data => {
        this.info = data.data
        uni.setStorageSync('brand_id', data.data.brand_id)
        uni.setStorageSync('merchant_id', data.data.merchant_id)
        uni.setStorageSync('__xingyun_userinfo__', {
          merchant: [
            {
              merchant_id: data.data.merchant_id,
              brand_id: data.data.brand_id
            }
          ]
        })
        if (!this.$store.state.jintiku.token) {
          this.exchange_codes = this.info.exchange_codes
        }
      })
      this.getCodeList()
    },
    getCodeList() {
      if (!this.$store.state.jintiku.token) {
        return
      }
      getCodes({
        id: this.id,
        identity: this.$store.state.jintiku.token ? 1 : ''
      }).then(data => {
        this.exchange_codes = data.data.exchange_codes
      })
    },
    getTime(timeStr) {
      return this.$xh.parseTimeDay(+new Date(timeStr) / 1000)
    },
    getCode(item, i) {
      if (!this.$store.state.jintiku.token) {
        setCode({
          index: i * 1 + 1,
          id: this.info.id
        }).then(data => {
          this.show = true
        })
        return
      }
      if (item.is_receive == '1') {
        this.$xh.Toast('已领取！')
        return
      }
      if (this.info.is_usable != '1') {
        this.$xh.Toast('兑换未开放！')
        return
      }
      if (item.is_share != '1') {
        this.index = i * 1 + 1
        // 弹出分享
        this.shre = true
        return
      }

      setCode({
        index: i * 1 + 1,
        id: this.info.id
      }).then(data => {
        this.show = true
      })
    }
  }
}
//#endif
</script>
<style scoped lang="less">
/* #ifdef H5 */
.code-receive {
  height: 100vh;
  background-color: #fff;
  padding: 32rpx;
  padding-top: 0;
  .head {
    height: 80rpx;
    line-height: 80rpx;
    font-size: 34rpx;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 800;
    color: #000000;
    text-align: center;
  }
  .time {
    font-size: 32rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    line-height: 32rpx;
    text-align: center;
    margin-bottom: 20rpx;
  }
  .card {
    width: 686rpx;
    // height: 476rpx;
    background: #f2f5f7;
    border-radius: 16rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
    .title {
      font-size: 30rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #000000;
      line-height: 28rpx;
      text-align: center;
      margin-bottom: 40rpx;
    }
    .lq {
      .flex {
        width: 100%;
        border-radius: 20rpx;
        overflow: hidden;
        margin-bottom: 32rpx;
        display: flex;
        align-items: center;
        height: 88rpx;
        .code {
          width: 354rpx;
          height: 88rpx;
          background: #ffffff;
          border-radius: 20rpx 0rpx 0rpx 20rpx;
          font-size: 28rpx;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: rgba(3, 32, 61, 0.85);
          line-height: 88rpx;
          text-align: center;
        }
        .btn {
          height: 88rpx;
          background: #1469ff;
          border-radius: 0rpx 20rpx 20rpx 0rpx;
          line-height: 88rpx;
          font-size: 28rpx;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          flex: 1;
          text-align: center;
        }
        .share {
          background: #ebf1ff;
          color: #1469ff;
        }
      }
    }
  }
  .code-duihuan {
    .title {
      display: flex;
      align-items: center;
      .line {
        width: 6rpx;
        height: 28rpx;
        background: #1469ff;
        border-radius: 5rpx;
        margin-right: 8rpx;
      }
      .title {
        font-size: 32rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 800;
        color: #000000;
        line-height: 28rpx;
      }
    }
    .desc {
      margin-top: 28rpx;
      .desc-list {
        font-size: 28rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
        line-height: 52rpx;
      }
    }
  }
  .success-mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    .success-wrapper {
      width: 600rpx;
      height: 528rpx;
      background: url('https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/1700793112528702a170079311252819859_bg.png')
        no-repeat;
      background-size: contain;
      margin-top: -180rpx;
      .head {
        display: flex;
        align-items: flex-start;
        justify-content: center;
        margin-top: -40rpx;
        image {
          width: 280rpx;
          margin-left: 45rpx;
        }
      }
      .gx {
        font-size: 36rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #1469ff;
        line-height: 50rpx;
        text-align: center;
        margin-top: 140rpx;
      }
      .text {
        width: 308rpx;
        height: 120rpx;
        font-size: 28rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #787e8f;
        line-height: 50rpx;
        margin: 0 auto;
        margin-top: 28rpx;
        text-align: center;
        text {
          color: #1469ff;
        }
      }
      .btn {
        width: 260rpx;
        height: 72rpx;
        background: linear-gradient(180deg, #ffde21 0%, #fc8e10 100%);
        border-radius: 36rpx;
        margin: 0 auto;
        margin-top: 90rpx;
        color: #fff;
      }
      .close {
        width: 64rpx;
        height: 64rpx;
        background-color: rgba(255, 255, 255, 0.2);
        color: #fff;
        border-radius: 50%;
        margin: 0 auto;
        margin-top: 50rpx;
      }
    }
  }
  .share-box {
    background-color: rgba(0, 0, 0, 0.4);
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1000000;
    display: flex;
    justify-content: center;
    align-items: center;
    image {
      width: 50%;
      height: auto;
    }
    .right {
      position: absolute;
      right: 0;
      top: 0;
    }
  }
}
/* #endif */
</style>
