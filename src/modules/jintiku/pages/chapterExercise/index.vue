<template>
  <view class="chapterExercise">
    <view class="title"> 本题库共有{{ total }}道题</view>
    <treeChapter :list="lists" :type="type" :isfree="query.isfree == '1'" />
    <view style="height: 80rpx"></view>
    <kfQrcode></kfQrcode>
    <view class="preData box-show" v-if="preData.id">
      <view class="leftinfo"> 上次练习：{{ preData.sectionname }} </view>
      <view class="button flex-center" @click="goContinue(preData)">
        继续练习
      </view>
    </view>
  </view>
</template>
<script>
import { getChapterlist, chapterpackageTree } from '../../api/chapter'
import treeChapter from '../../components/treeChapter/index.vue'
import kfQrcode from '../../components/commen/kf-qrcode.vue'
import { shareAppMessage2 } from '../../utils/index.js'
import { setTimeInfo } from '../../api/userInfo'
export default {
  components: {
    treeChapter,
    kfQrcode
  },
  provide() {
    return {
      updatePrePriceData: this.updatePrePriceData
    }
  },
  data() {
    return {
      type: '1',
      lists: [],
      total: 0,
      preData: {}, // 上次练习位置
      query: {}
    }
  },
  onShow() {
    setTimeout(() => {
      this.getList()
    }, 200)
  },
  onLoad(e) {
    this.total = e.total
    this.query = e

    setTimeInfo({
      chapter_number: '3000'
    }).then(data => {})
  },
  methods: {
    getList() {
      if (this.query.professional_id) {
        this.type = '10'
        chapterpackageTree({
          professional_id: this.query.professional_id,
          goods_id: this.query.goods_id
        }).then(data => {
          let section_info = this.filter(data?.data?.section_info || [])
          if (section_info.length == 1) {
            if (section_info[0]?.child?.length) {
              section_info = section_info[0].child
            }
          }
          this.lists = section_info
          this.setParent(this.lists)
          this.getLast(section_info)
        })
      } else {
        getChapterlist().then(data => {
          this.lists = data?.data?.section_info
          this.setParent(this.lists)
          this.getLast(data?.data?.section_info)
        })
      }
    },
    filter(list) {
      return list
        .filter(e => e.question_number != '0')
        .map(item => {
          if (item?.child?.length > 0) {
            item.child = this.filter(item?.child)
          }
          return item
        })
    },
    getLast(list) {
      let lastList = []
      function forEach(arr) {
        for (let item of arr) {
          if (item?.child?.length > 0) {
            forEach(item.child)
          } else {
            if (item.question_number != '0') {
              lastList.push(item)
            }
          }
        }
      }
      forEach(list)
      uni.setStorageSync('__chapterLastList', lastList)
    },
    setParent(lists, prevLeval = 1) {
      lists?.forEach((item, i) => {
        item.leval = prevLeval
        if (item.child && item.child.length) {
          this.setParent(item.child, item.leval + 1)
        } else {
          // 判断是否有上一次练习
          if (item.section_type == '2' && item.is_checked == '1') {
            // 确认是知识点
            this.preData = item
          }
        }
      })
    },
    handleCallBack(lists, cb) {
      lists?.forEach((item, i) => {
        if (item.child && item.child.length) {
          cb && cb(item, false)
          this.handleCallBack(item.child, cb)
        } else {
          cb && cb(item, item.section_type == '2')
        }
      })
    },
    goContinue(item) {
      this.$xh.push(
        'jintiku',
        `pages/makeQuestion/makeQuestion?knowledge_id=${item.id}&type=1&chapter_id=${item.sectionprent}`
      )
    },
    // 更新当前练习
    updatePrePriceData(id) {
      this.handleCallBack(
        this.lists,
        (item /*每次遍历的元素*/, bol /*是否是子元素*/) => {
          item.is_checked = '0'
          if (bol && item.id == id) {
            item.is_checked = '1'
          }
        }
      )
    }
  },
  onShareAppMessage() {
    return shareAppMessage2()
  }
}
</script>
<style scoped lang="less">
.chapterExercise {
  height: 100vh;
  overflow-y: auto;
  background-color: #fff;
  padding-bottom: 100rpx;
  .big-title {
    color: #ccc;
    font-size: 28rpx;
    margin-top: 20rpx;
  }
  .title {
    height: 80rpx;
    line-height: 80rpx;
    background: #f5f5f5;
    font-size: 28rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    padding-left: 30rpx;
  }
  .preData {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 40rpx;
    margin: 0 auto;
    height: 70rpx;
    width: 90%;
    border-radius: 10rpx;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    .leftinfo {
      font-size: 24rpx;
      color: #333;
    }
    .button {
      width: 120rpx;
      height: 40rpx;
      background-color: #387dfc;
      color: #fff;
      font-size: 20rpx;
      border-radius: 10rpx;
    }
  }
}
</style>
