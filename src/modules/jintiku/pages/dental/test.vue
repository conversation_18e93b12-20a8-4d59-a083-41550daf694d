<template>
  <view class="test-page">
    <view class="test-section">
      <text class="test-title">组件测试页面</text>
      
      <!-- 测试 section-title 组件 -->
      <section-title title="章节标题测试" />
      
      <!-- 测试 practice-card 组件 -->
      <view class="test-group">
        <text class="group-title">练习卡片组件测试</text>
        <practice-card
          icon="https://via.placeholder.com/80x80/FF6B35/FFFFFF?text=Icon"
          title="测试练习项目"
          subtitle="这是一个测试副标题"
          @click="handleTestClick"
        />
      </view>
      
      <!-- 测试 exam-item 组件 -->
      <view class="test-group">
        <text class="group-title">考试项目组件测试</text>
        <exam-item
          title="测试考试项目"
          :questionCount="100"
          duration="永久"
          difficulty="中等"
          :showProgress="true"
          :currentProgress="30"
          :totalProgress="100"
          actionText="开始测试"
          @click="handleTestExamClick"
          @action="handleTestExamAction"
        />
      </view>
      
      <!-- 测试带时间的考试项目 -->
      <view class="test-group">
        <exam-item
          title="定时考试测试"
          :questionCount="50"
          duration="120分钟"
          difficulty="困难"
          startTime="2025-04-28 00:00:00"
          actionText="立即参加"
          :isPrimary="true"
          @click="handleTestExamClick"
          @action="handleTestExamAction"
        />
      </view>
    </view>
  </view>
</template>

<script>
import practiceCard from '../../components/commen/practice-card.vue'
import examItem from '../../components/commen/exam-item.vue'
import sectionTitle from '../../components/commen/section-title.vue'

export default {
  components: {
    practiceCard,
    examItem,
    sectionTitle
  },
  
  methods: {
    handleTestClick(data) {
      console.log('测试点击练习卡片:', data)
      uni.showToast({
        title: '点击了练习卡片',
        icon: 'none'
      })
    },
    
    handleTestExamClick(data) {
      console.log('测试点击考试项目:', data)
      uni.showToast({
        title: '点击了考试项目',
        icon: 'none'
      })
    },
    
    handleTestExamAction(data) {
      console.log('测试点击考试操作:', data)
      uni.showToast({
        title: '点击了考试操作按钮',
        icon: 'none'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 32rpx;
  background: #f5f6f7;
  min-height: 100vh;
}

.test-section {
  .test-title {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    text-align: center;
    margin-bottom: 40rpx;
  }
}

.test-group {
  margin-bottom: 40rpx;
  
  .group-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #666;
    margin-bottom: 16rpx;
  }
}
</style>
