<template>
  <view class="demo-page">
    <view class="demo-header">
      <text class="demo-title">口腔执业页面演示</text>
      <text class="demo-subtitle">点击下方按钮查看不同页面</text>
    </view>
    
    <view class="demo-buttons">
      <button class="demo-btn primary" @click="goToMainPage">
        📱 查看主页面
      </button>
      
      <button class="demo-btn secondary" @click="goToTestPage">
        🧪 查看组件测试
      </button>
      
      <button class="demo-btn info" @click="showInfo">
        📖 查看开发说明
      </button>
    </view>
    
    <view class="demo-info">
      <view class="info-section">
        <text class="info-title">✨ 已实现功能</text>
        <view class="info-list">
          <text class="info-item">• 自定义导航栏（带倒计时）</text>
          <text class="info-item">• 笔试精讲班广告横幅</text>
          <text class="info-item">• 练习功能网格布局（2x2）</text>
          <text class="info-item">• 章节模考列表</text>
          <text class="info-item">• 科目模考列表</text>
          <text class="info-item">• 模拟考试列表</text>
          <text class="info-item">• 可复用组件设计</text>
        </view>
      </view>
      
      <view class="info-section">
        <text class="info-title">🧩 组件列表</text>
        <view class="info-list">
          <text class="info-item">• practice-card.vue - 练习卡片</text>
          <text class="info-item">• exam-item.vue - 考试项目</text>
          <text class="info-item">• section-title.vue - 章节标题</text>
        </view>
      </view>
      
      <view class="info-section">
        <text class="info-title">📁 文件位置</text>
        <view class="info-list">
          <text class="info-item">• 主页面：pages/dental/index.vue</text>
          <text class="info-item">• 测试页面：pages/dental/test.vue</text>
          <text class="info-item">• 组件：components/commen/</text>
          <text class="info-item">• 文档：docs/dental-page-guide.md</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    goToMainPage() {
      uni.navigateTo({
        url: '/modules/jintiku/pages/dental/index'
      })
    },
    
    goToTestPage() {
      uni.navigateTo({
        url: '/modules/jintiku/pages/dental/test'
      })
    },
    
    showInfo() {
      uni.showModal({
        title: '开发说明',
        content: '页面已按照截图要求开发完成，包含所有功能模块和可复用组件。详细说明请查看 docs/dental-page-guide.md 文档。',
        showCancel: false,
        confirmText: '知道了'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.demo-page {
  padding: 40rpx;
  background: #f5f6f7;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .demo-title {
    display: block;
    font-size: 40rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }
  
  .demo-subtitle {
    display: block;
    font-size: 28rpx;
    color: #666;
  }
}

.demo-buttons {
  margin-bottom: 60rpx;
  
  .demo-btn {
    width: 100%;
    height: 88rpx;
    border-radius: 16rpx;
    font-size: 32rpx;
    font-weight: 600;
    margin-bottom: 24rpx;
    border: none;
    
    &.primary {
      background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%);
      color: #fff;
    }
    
    &.secondary {
      background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
      color: #fff;
    }
    
    &.info {
      background: linear-gradient(135deg, #2196F3 0%, #42A5F5 100%);
      color: #fff;
    }
    
    &:active {
      opacity: 0.8;
    }
  }
}

.demo-info {
  .info-section {
    background: #fff;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
    
    .info-title {
      display: block;
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .info-list {
      .info-item {
        display: block;
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
        margin-bottom: 12rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
