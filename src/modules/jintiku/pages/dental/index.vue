<template>
  <view class="dental-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <text class="navbar-title">口腔执业</text>
        <view class="navbar-subtitle">
          <text>距离医学综合考试还有</text>
          <text class="highlight">46天</text>
        </view>
      </view>
    </view>

    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 笔试精讲班广告 -->
      <view class="ad-banner" @click="handleAdClick">
        <view class="ad-content">
          <view class="ad-title">笔试精讲班</view>
          <view class="ad-subtitle">逐章节考点精讲，形成知识体系，全面巩固基础</view>
          <view class="ad-features">
            <text>全题通</text>
            <text>一键解锁 全部题库+工具+考点+密卷</text>
          </view>
        </view>
        <view class="ad-icon">📚</view>
      </view>

      <!-- 练习功能区 -->
      <view class="practice-section">
        <view class="practice-grid">
          <practice-card
            v-for="item in practiceItems"
            :key="item.id"
            :icon="item.icon"
            :title="item.title"
            :subtitle="item.subtitle"
            :url="item.url"
            @click="handlePracticeClick"
          />
        </view>
      </view>

      <!-- 章节模考 -->
      <section-title title="章节模考" />
      <exam-item
        :title="chapterExam.title"
        :questionCount="chapterExam.questionCount"
        :duration="chapterExam.duration"
        :difficulty="chapterExam.difficulty"
        :showProgress="true"
        :currentProgress="chapterExam.currentProgress"
        :totalProgress="chapterExam.totalProgress"
        :actionText="chapterExam.actionText"
        :examData="chapterExam"
        @click="handleExamClick"
        @action="handleExamAction"
      />

      <!-- 科目模考 -->
      <section-title title="科目模考" />
      <exam-item
        :title="subjectExam.title"
        :questionCount="subjectExam.questionCount"
        :duration="subjectExam.duration"
        :difficulty="subjectExam.difficulty"
        :startTime="subjectExam.startTime"
        :actionText="subjectExam.actionText"
        :examData="subjectExam"
        @click="handleExamClick"
        @action="handleExamAction"
      />

      <!-- 模拟考试 -->
      <section-title title="模拟考试" />
      <exam-item
        :title="mockExam.title"
        :questionCount="mockExam.questionCount"
        :duration="mockExam.duration"
        :difficulty="mockExam.difficulty"
        :startTime="mockExam.startTime"
        :actionText="mockExam.actionText"
        :examData="mockExam"
        @click="handleExamClick"
        @action="handleExamAction"
      />
    </view>

    <!-- 底部导航栏占位 -->
    <view class="tabbar-placeholder"></view>
  </view>
</template>

<script>
import practiceCard from '../../components/commen/practice-card.vue'
import examItem from '../../components/commen/exam-item.vue'
import sectionTitle from '../../components/commen/section-title.vue'

export default {
  components: {
    practiceCard,
    examItem,
    sectionTitle
  },
  
  data() {
    return {
      // 练习功能数据
      practiceItems: [
        {
          id: 1,
          icon: 'https://via.placeholder.com/80x80/FF6B35/FFFFFF?text=💪',
          title: '核心突破5000题',
          subtitle: '冲刺必刷 快速提分',
          url: '/modules/jintiku/pages/chapterExercise/index?type=core'
        },
        {
          id: 2,
          icon: 'https://via.placeholder.com/80x80/4CAF50/FFFFFF?text=📈',
          title: '能力提升',
          subtitle: '阶段测试',
          url: '/modules/jintiku/pages/intelligentEvaluation/index'
        },
        {
          id: 3,
          icon: 'https://via.placeholder.com/80x80/2196F3/FFFFFF?text=📝',
          title: '笔试必刷4000题',
          subtitle: '重点难点总结',
          url: '/modules/jintiku/pages/chapterExercise/index?type=must'
        },
        {
          id: 4,
          icon: 'https://via.placeholder.com/80x80/9C27B0/FFFFFF?text=🎯',
          title: '理论阶段模考题',
          subtitle: '理论强化',
          url: '/modules/jintiku/pages/modelExaminationCompetition/index'
        }
      ],
      
      // 章节模考数据
      chapterExam: {
        title: '口腔实践技能题库',
        questionCount: 105,
        duration: '永久',
        difficulty: '永久',
        currentProgress: 0,
        totalProgress: 105,
        actionText: '立即刷题'
      },
      
      // 科目模考数据
      subjectExam: {
        title: '口腔执业25年万人模考第一轮',
        questionCount: 48,
        duration: '永久',
        difficulty: '永久',
        startTime: '2025-04-28 00:00:00',
        actionText: '立即刷题'
      },
      
      // 模拟考试数据
      mockExam: {
        title: '口腔执业25年万人模考第一轮',
        questionCount: 48,
        duration: '永久',
        difficulty: '永久',
        startTime: '2025-04-28 00:00:00',
        actionText: '立即刷题'
      }
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },
  
  methods: {
    initPage() {
      // 初始化页面数据
      console.log('初始化口腔执业页面')
    },
    
    refreshData() {
      // 刷新页面数据
      console.log('刷新页面数据')
    },
    
    handleAdClick() {
      console.log('点击广告banner')
      uni.navigateTo({
        url: '/modules/jintiku/pages/test/detail?type=course'
      })
    },
    
    handlePracticeClick(item) {
      console.log('点击练习项目:', item)
      // 可以在这里添加统计埋点
    },
    
    handleExamClick(examData) {
      console.log('点击考试项目:', examData)
      // 跳转到考试详情页
      uni.navigateTo({
        url: '/modules/jintiku/pages/examination/index'
      })
    },
    
    handleExamAction(examData) {
      console.log('点击考试操作按钮:', examData)
      // 根据考试类型跳转到不同页面
      if (examData.title.includes('实践技能')) {
        uni.navigateTo({
          url: '/modules/jintiku/pages/makeQuestion/makeQuestion?type=skill'
        })
      } else {
        uni.navigateTo({
          url: '/modules/jintiku/pages/makeQuestion/makeQuestion?type=theory'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dental-page {
  min-height: 100vh;
  background: #f5f6f7;
}

.custom-navbar {
  background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%);
  padding: 0 32rpx;
  padding-top: var(--status-bar-height, 44rpx);
  
  .navbar-content {
    height: 88rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    
    .navbar-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #fff;
      margin-bottom: 4rpx;
    }
    
    .navbar-subtitle {
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.9);
      
      .highlight {
        color: #fff;
        font-weight: 600;
      }
    }
  }
}

.page-content {
  padding: 24rpx 32rpx;
}

.ad-banner {
  position: relative;
  height: 160rpx;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
  background: linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;

  .ad-content {
    padding: 24rpx;
    color: #fff;
    flex: 1;

    .ad-title {
      font-size: 32rpx;
      font-weight: 600;
      margin-bottom: 8rpx;
    }

    .ad-subtitle {
      font-size: 24rpx;
      opacity: 0.9;
      margin-bottom: 12rpx;
    }

    .ad-features {
      font-size: 22rpx;
      opacity: 0.8;

      text:first-child {
        margin-right: 16rpx;
      }
    }
  }

  .ad-icon {
    width: 120rpx;
    height: 120rpx;
    margin-right: 24rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 60rpx;
  }
}

.practice-section {
  margin-bottom: 16rpx;
  
  .practice-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16rpx;
  }
}

.tabbar-placeholder {
  height: 100rpx;
}
</style>
