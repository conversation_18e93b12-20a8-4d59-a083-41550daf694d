<template>
  <view class="test-page">
    <view class="header">
      <text class="page-title">学习卡片网格组件测试</text>
    </view>
    
    <!-- 使用学习卡片网格组件 -->
    <study-card-grid @cardClick="handleCardClick" />
    
    <view class="test-info">
      <view class="section">
        <text class="section-title">点击记录</text>
        <view class="click-log">
          <text v-if="!clickHistory.length" class="no-data">暂无点击记录</text>
          <view 
            v-for="(item, index) in clickHistory" 
            :key="index"
            class="log-item"
          >
            <text class="log-time">{{ item.time }}</text>
            <text class="log-type">{{ item.name }}</text>
          </view>
        </view>
      </view>
      
      <view class="section">
        <text class="section-title">组件说明</text>
        <view class="description">
          <text class="desc-text">• 基于 uView UI Grid 组件构建</text>
          <text class="desc-text">• 2x2 网格布局，4个学习模块</text>
          <text class="desc-text">• 支持点击事件和页面跳转</text>
          <text class="desc-text">• 渐变色背景，视觉效果佳</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import StudyCardGrid from '../../components/commen/study-card-grid.vue'

export default {
  name: 'StudyCardGridTest',
  components: {
    StudyCardGrid
  },
  data() {
    return {
      clickHistory: []
    }
  },
  methods: {
    handleCardClick(type) {
      console.log('点击了卡片:', type)
      
      // 记录点击历史
      const cardNames = {
        'core-breakthrough': '核心突破5000题',
        'ability-improvement': '能力提升',
        'must-practice': '必刷4000题',
        'theory-mock': '理论阶段模考题'
      }
      
      this.clickHistory.unshift({
        time: this.formatTime(new Date()),
        type: type,
        name: cardNames[type] || '未知模块'
      })
      
      // 只保留最近10条记录
      if (this.clickHistory.length > 10) {
        this.clickHistory = this.clickHistory.slice(0, 10)
      }
      
      // 显示提示
      uni.showToast({
        title: `点击了${cardNames[type]}`,
        icon: 'none',
        duration: 1500
      })
    },
    
    formatTime(date) {
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      const seconds = date.getSeconds().toString().padStart(2, '0')
      return `${hours}:${minutes}:${seconds}`
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  min-height: 100vh;
  background: #f5f5f5;
  
  .header {
    padding: 32rpx 24rpx 16rpx;
    background: #fff;
    
    .page-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }
  }
  
  .test-info {
    margin-top: 24rpx;
    
    .section {
      background: #fff;
      margin-bottom: 24rpx;
      padding: 32rpx 24rpx;
      
      .section-title {
        display: block;
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 24rpx;
      }
      
      .click-log {
        .no-data {
          color: #999;
          font-size: 28rpx;
          text-align: center;
          padding: 40rpx 0;
        }
        
        .log-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16rpx 0;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
          
          .log-time {
            font-size: 24rpx;
            color: #999;
          }
          
          .log-type {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
          }
        }
      }
      
      .description {
        .desc-text {
          display: block;
          font-size: 28rpx;
          color: #666;
          line-height: 1.6;
          margin-bottom: 12rpx;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
</style>
