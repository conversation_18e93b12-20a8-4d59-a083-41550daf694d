<template>
  <view class="exam-item" @click="handleClick">
    <view class="exam-header">
      <text class="exam-title">{{ title }}</text>
      <view class="exam-tags">
        <text class="exam-tag">共{{ questionCount }}题</text>
        <text class="exam-tag">{{ duration }}</text>
        <text class="exam-tag">{{ difficulty }}</text>
      </view>
    </view>
    
    <view class="exam-info">
      <text class="exam-time" v-if="startTime">开考时间：{{ startTime }}</text>
    </view>
    
    <view class="exam-progress" v-if="showProgress">
      <view class="progress-bar">
        <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
      </view>
      <text class="progress-text">{{ currentProgress }}/{{ totalProgress }}</text>
    </view>
    
    <view class="exam-actions">
      <button 
        class="exam-btn" 
        :class="{ 'primary': isPrimary }"
        @click.stop="handleAction"
      >
        {{ actionText }}
      </button>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ExamItem',
  props: {
    title: {
      type: String,
      required: true
    },
    questionCount: {
      type: [String, Number],
      default: 0
    },
    duration: {
      type: String,
      default: '永久'
    },
    difficulty: {
      type: String,
      default: '中等'
    },
    startTime: {
      type: String,
      default: ''
    },
    showProgress: {
      type: Boolean,
      default: false
    },
    currentProgress: {
      type: [String, Number],
      default: 0
    },
    totalProgress: {
      type: [String, Number],
      default: 0
    },
    actionText: {
      type: String,
      default: '立即刷题'
    },
    isPrimary: {
      type: Boolean,
      default: true
    },
    examData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    progressPercent() {
      if (!this.showProgress || !this.totalProgress) return 0
      return Math.round((this.currentProgress / this.totalProgress) * 100)
    }
  },
  methods: {
    handleClick() {
      this.$emit('click', this.examData)
    },
    
    handleAction() {
      this.$emit('action', this.examData)
    }
  }
}
</script>

<style lang="scss" scoped>
.exam-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  
  .exam-header {
    margin-bottom: 16rpx;
    
    .exam-title {
      display: block;
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 12rpx;
    }
    
    .exam-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;
      
      .exam-tag {
        padding: 8rpx 16rpx;
        background: #f0f0f0;
        border-radius: 8rpx;
        font-size: 24rpx;
        color: #666;
      }
    }
  }
  
  .exam-info {
    margin-bottom: 16rpx;
    
    .exam-time {
      font-size: 28rpx;
      color: #666;
    }
  }
  
  .exam-progress {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    
    .progress-bar {
      flex: 1;
      height: 8rpx;
      background: #f0f0f0;
      border-radius: 4rpx;
      margin-right: 16rpx;
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #FF6B35 0%, #FF8E53 100%);
        border-radius: 4rpx;
        transition: width 0.3s ease;
      }
    }
    
    .progress-text {
      font-size: 24rpx;
      color: #666;
      min-width: 80rpx;
    }
  }
  
  .exam-actions {
    display: flex;
    justify-content: flex-end;
    
    .exam-btn {
      padding: 16rpx 32rpx;
      border-radius: 24rpx;
      font-size: 28rpx;
      border: 1px solid #ddd;
      background: #fff;
      color: #666;
      
      &.primary {
        background: linear-gradient(90deg, #FF6B35 0%, #FF8E53 100%);
        color: #fff;
        border: none;
      }
      
      &:active {
        opacity: 0.8;
      }
    }
  }
}
</style>
