<template>
  <view>
    <view
      v-if="page_type == 'home'"
      class="chapter-exercise"
      @click="goDetail2(info)"
    >
      <image
        src="https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16986562697231fe4169865626972412207_sj.png"
        mode="widthFix"
        class="testPaperImg"
      />
      <view class="test-paper-process">
        <view class="test-paper-title">
          <view class="paper-title">章节练习</view>
          <view class="paper-desc"> 日拱一卒，快开启今日的练习吧～ </view>
        </view>
        <view class="process">
          <view
            class="current"
            :style="{
              width: num + '%'
            }"
          ></view>
        </view>
        <view class="process-text">
          <view class="process-success"> 已完成{{ num }}% </view>
          <view class="process-static">
            {{ info.do_question_num }}/{{ info.question_number }}
          </view>
        </view>
      </view>
    </view>
    <view style="height: 32rpx"></view>
    <view class="navs" v-if="false">
      <view class="nav" v-for="(item, i) in navs" :key="i">
        <image :src="item.img" mode="widthFix" @click="goDetail(item.url)" />
        <text @click="goDetail(item.url)">{{ item.name }}</text>
      </view>
    </view>
  </view>
</template>
<script>
import { index, chapterpackage, getOrderV2 } from '../../api/index'
import { app_id } from '../../config'
import { goToLogin, setSubscribeMessage } from '../../utils/index'
export default {
  props: {
    page_type: {
      type: String,
      default: 'home'
    }
  },
  data() {
    return {
      navs: [
        {
          name: '真题闯关',
          img: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16986565033678e7f169865650336724144_1.png',
          url: 'pages/questionChallenge/index'
        },
        {
          name: '智能测评',
          img: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/169865652858773fa169865652858785885_2.png',
          url: 'pages/intelligentEvaluation/index'
        },
        {
          name: '考点词条',
          img: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/771e173976064255986506_kaodian.png',
          url: 'pages/examEntry/index'
        },
        {
          name: '模考大赛',
          img: 'https://ysys-assets.oss-cn-beijing.aliyuncs.com/public/16986565946988bf0169865659469836993_4.png',
          url: 'pages/modelExaminationCompetition/index'
        }
      ],
      info: {
        collect_question_num: '0',
        do_question_num: '0', // 已做题数
        err_question_num: '0', // 错题
        question_number: '0' // 章节题目数
      }
    }
  },
  created() {
    // this.init()
  },
  computed: {
    num() {
      if (this.info?.do_question_num == 0) {
        return 0
      } else {
        return (
          (this.info.do_question_num / this.info.question_number) *
          100
        ).toFixed(2)
      }
    }
  },
  methods: {
    goDetail2(item) {
      if (!this.$store.state.jintiku.token) {
        console.log('没有', this.$store.state.jintiku.token)
        goToLogin()
        return
      }
      setSubscribeMessage('login')
      if (item.id == '0' || !item.id) {
        this.$xh.Toast('暂无免费章节练习！')
        return
      }
      if (item.permission_status != '1') {
        this.getOrder()
      }
      this.$xh.push(
        'jintiku',
        `pages/chapterExercise/index?professional_id=${item.professional_id}&goods_id=${item.id}&total=${this.info.question_number}&isfree=1`
      )
    },
    //支付点击
    async getOrder() {
      let payable_amount = 0
      let student_id = uni.getStorageSync('__xingyun_userinfo__').student_id
      let employee_id = this.$store.state.jintiku.employee_id
      let goods_id = this.info.id
      let data = {
        business_scene: 1,
        goods: [
          {
            goods_id: goods_id,
            // goods_months_price_id: '',
            // months: this.info.month,
            class_campus_id: '',
            class_city_id: '',
            goods_num: '1'
          }
        ],
        deposit_amount: Number(payable_amount),
        payable_amount: Number(payable_amount),
        real_amount: Number(payable_amount),
        remark: '',
        student_adddatas_id: '',
        student_id: student_id,
        total_amount: Number(payable_amount),
        app_id: app_id,
        pay_method: '',
        order_type: 10,
        discount_amount: 0,
        coupons_ids: [],
        employee_id: employee_id || '508948528815416786',
        delivery_type: 1 // 默认总部邮寄
      }
      getOrderV2({
        ...data
      }).then(res => {
        this.info.permission_status = 1
      })
    },
    isLogin() {
      return !!this.$store.state.jintiku.token
    },
    init() {
      // if (!this.isLogin()) {
      //   return
      // }

      chapterpackage({
        professional_id: uni.getStorageSync('__xingyun_major__').major_id || '',
        noloading: true
      }).then(res => {
        this.info = {
          collect_question_num: '0',
          do_question_num: res.data.student_finish.do_num, // 已做题数
          err_question_num: '0', // 错题
          question_number: res.data.question_num, // 章节题目数
          ...res.data
        }
      })
      // index
      //   .static({
      //     noloading: true
      //   })
      //   .then(data => {
      //     this.info = data.data
      //   })
    },
    goDetail(url) {
      if (!this.isLogin()) {
        // 去登录
        // this.$xh.push('jintiku', 'pages/loginCenter/index')
        goToLogin('goToLogin11')
        return
      }
      this.$xh.push('jintiku', url)
    }
  }
}
</script>
<style scoped lang="less">
.chapter-exercise {
  height: 180rpx;
  background-color: #eff5ff;
  display: flex;
  padding: 40rpx 32rpx;
  border-radius: 16rpx;
  .testPaperImg {
    width: 80rpx;
    height: 100rpx;
    margin-right: 32rpx;
  }
  .test-paper-process {
    flex: 1;
    .test-paper-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 14rpx;
      .paper-title {
        font-size: 30rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        color: #161f30;
        line-height: 30rpx;
      }
      .paper-desc {
        font-size: 24rpx;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 700;
        color: #2e68ff;
        line-height: 24rpx;
      }
    }
    .process {
      width: 100%;
      height: 16rpx;
      background: #ffffff;
      border-radius: 15rpx;
      opacity: 0.6;
      margin-bottom: 16rpx;
      position: relative;
      .current {
        height: 100%;
        background-color: #2e68ff;
        border-radius: 15rpx;
        position: absolute;
        left: 0;
        top: 0;
        transition: all 0.25s;
      }
    }
    .process-text {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 24rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      // color: #999999;
      color: rgba(3, 32, 61, 0.85);
      line-height: 24rpx;
    }
  }
}
.navs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 34rpx;
  padding: 0 30rpx;
  margin-bottom: 48rpx;
  .nav {
    display: flex;
    flex-direction: column;
    align-items: center;
    image {
      width: 52rpx;
      height: 52rpx;
      margin-bottom: 20rpx;
    }
    text {
      font-size: 26rpx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #2f465f;
      line-height: 26rpx;
    }
  }
}
</style>
