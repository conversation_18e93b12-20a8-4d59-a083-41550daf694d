<template>
  <view class="section-title">
    <view class="title-line"></view>
    <text class="title-text">{{ title }}</text>
  </view>
</template>

<script>
export default {
  name: 'SectionTitle',
  props: {
    title: {
      type: String,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.section-title {
  display: flex;
  align-items: center;
  margin: 32rpx 0 24rpx 0;
  
  .title-line {
    width: 8rpx;
    height: 32rpx;
    background: linear-gradient(180deg, #FF6B35 0%, #FF8E53 100%);
    border-radius: 4rpx;
    margin-right: 16rpx;
  }
  
  .title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}
</style>
