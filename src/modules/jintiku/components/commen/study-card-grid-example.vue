<template>
  <view class="page">
    <view class="header">
      <text class="page-title">学习模块</text>
    </view>
    
    <!-- 使用学习卡片网格组件 -->
    <study-card-grid @cardClick="handleCardClick" />
    
    <view class="tips">
      <text class="tips-text">选择适合的学习模块，开始你的学习之旅</text>
    </view>
  </view>
</template>

<script>
import StudyCardGrid from './study-card-grid.vue'

export default {
  name: 'StudyCardGridExample',
  components: {
    StudyCardGrid
  },
  methods: {
    handleCardClick(type) {
      console.log('点击了卡片:', type)
      
      // 可以在这里添加统计埋点或其他逻辑
      uni.showToast({
        title: `进入${this.getCardName(type)}`,
        icon: 'none'
      })
    },
    
    getCardName(type) {
      const names = {
        'core-breakthrough': '核心突破5000题',
        'ability-improvement': '能力提升',
        'must-practice': '必刷4000题',
        'theory-mock': '理论阶段模考题'
      }
      return names[type] || '未知模块'
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: #f5f5f5;
  
  .header {
    padding: 32rpx 24rpx 16rpx;
    background: #fff;
    
    .page-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }
  }
  
  .tips {
    padding: 24rpx;
    text-align: center;
    
    .tips-text {
      font-size: 26rpx;
      color: #999;
    }
  }
}
</style>
