# StudyCardGrid 学习卡片网格组件

## 组件描述

这是一个基于 uView UI Grid 组件的学习模块卡片网格组件，包含4个不同颜色和功能的学习卡片，采用2x2网格布局。

## 功能特性

- 🎨 **美观设计**: 采用渐变色背景，视觉效果佳
- 📱 **响应式**: 支持不同屏幕尺寸自适应
- 🎯 **交互友好**: 点击反馈和页面跳转
- 🔧 **易于扩展**: 支持自定义事件处理
- 🛠️ **基于 uView UI**: 使用成熟的 Grid 组件，稳定可靠

## 卡片内容

1. **核心突破5000题** (橙色)
   - 图标: 💎
   - 描述: 冲刺必刷 快速提分
   - 跳转: `/pages/chapterExercise/index?type=core`

2. **能力提升** (红色)
   - 图标: 🔔
   - 描述: 阶段测试
   - 跳转: `/pages/intelligentEvaluation/index`

3. **必刷4000题** (蓝色)
   - 图标: 🦷
   - 描述: 重难点总结
   - 跳转: `/pages/chapterExercise/index?type=must`

4. **理论阶段模考题** (紫色)
   - 图标: 📝
   - 描述: 理论强化
   - 跳转: `/pages/examination/index?type=theory`

## 使用方法

### 基础使用

```vue
<template>
  <view>
    <study-card-grid @cardClick="handleCardClick" />
  </view>
</template>

<script>
import StudyCardGrid from '@/components/commen/study-card-grid.vue'

export default {
  components: {
    StudyCardGrid
  },
  methods: {
    handleCardClick(type) {
      console.log('点击了卡片:', type)
      // 自定义处理逻辑
    }
  }
}
</script>
```

### 依赖要求

- **uView UI 2.0+**: 组件基于 uView UI 的 Grid 组件构建
- **Vue 2.6+**: 支持 Vue 2.6 及以上版本
- **UniApp**: 需要在 UniApp 环境中使用

### 事件说明

#### @cardClick
- **类型**: `Function`
- **参数**: `type` (String) - 卡片类型
- **说明**: 卡片点击事件，返回卡片类型标识

卡片类型标识：
- `core-breakthrough`: 核心突破5000题
- `ability-improvement`: 能力提升
- `must-practice`: 必刷4000题
- `theory-mock`: 理论阶段模考题

## 样式定制

组件使用了SCSS，支持以下自定义：

```scss
// 修改卡片间距
.study-card-grid {
  padding: 32rpx; // 外边距
  
  .card-row {
    gap: 20rpx; // 卡片间距
  }
}

// 修改卡片颜色
.study-card {
  &.card-orange {
    background: linear-gradient(135deg, #your-color 0%, #your-color 100%);
  }
}
```

## 注意事项

1. **依赖 uView UI**: 确保项目已正确安装和配置 uView UI
2. **页面路径**: 确保目标页面路径存在
3. **图标兼容性**: 图标使用emoji，在某些设备上可能显示效果不同
4. **图片图标**: 如需使用图片图标，请修改对应的图标部分
5. **框架依赖**: 组件依赖 UniApp 框架

## 文件结构

```
components/commen/
├── study-card-grid.vue          # 主组件
├── study-card-grid-example.vue  # 使用示例
└── study-card-grid-README.md    # 说明文档
```
