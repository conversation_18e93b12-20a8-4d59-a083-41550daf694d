<template>
  <view class="study-card-grid">
    <u-grid
      :border="false"
      :col="2"
      @click="handleGridClick"
    >
      <!-- 核心突破5000题 -->
      <u-grid-item
        name="core-breakthrough"
        :customStyle="cardStyle"
      >
        <view class="study-card card-orange">
          <view class="card-icon">
            <view class="icon-diamond">💎</view>
          </view>
          <view class="card-content">
            <text class="card-title">核心突破5000题</text>
            <text class="card-subtitle">冲刺必刷 快速提分</text>
          </view>
        </view>
      </u-grid-item>

      <!-- 能力提升 -->
      <u-grid-item
        name="ability-improvement"
        :customStyle="cardStyle"
      >
        <view class="study-card card-red">
          <view class="card-icon">
            <view class="icon-bell">🔔</view>
          </view>
          <view class="card-content">
            <text class="card-title">能力提升</text>
            <text class="card-subtitle">阶段测试</text>
          </view>
        </view>
      </u-grid-item>

      <!-- 必刷4000题 -->
      <u-grid-item
        name="must-practice"
        :customStyle="cardStyle"
      >
        <view class="study-card card-blue">
          <view class="card-icon">
            <view class="icon-tooth">🦷</view>
          </view>
          <view class="card-content">
            <text class="card-title">必刷4000题</text>
            <text class="card-subtitle">重难点总结</text>
          </view>
        </view>
      </u-grid-item>

      <!-- 理论阶段模考题 -->
      <u-grid-item
        name="theory-mock"
        :customStyle="cardStyle"
      >
        <view class="study-card card-purple">
          <view class="card-icon">
            <view class="icon-edit">📝</view>
          </view>
          <view class="card-content">
            <text class="card-title">理论阶段模考题</text>
            <text class="card-subtitle">理论强化</text>
          </view>
        </view>
      </u-grid-item>
    </u-grid>
  </view>
</template>

<script>
export default {
  name: 'StudyCardGrid',
  data() {
    return {
      // 自定义卡片样式
      cardStyle: {
        padding: '10rpx',
        height: '140rpx'
      }
    }
  },
  methods: {
    // 处理 u-grid 的点击事件
    handleGridClick(name) {
      this.handleCardClick(name)
    },

    handleCardClick(type) {
      this.$emit('cardClick', type)

      // 根据不同类型跳转到不同页面
      switch (type) {
        case 'core-breakthrough':
          uni.navigateTo({
            url: '/pages/chapterExercise/index?type=core'
          })
          break
        case 'ability-improvement':
          uni.navigateTo({
            url: '/pages/intelligentEvaluation/index'
          })
          break
        case 'must-practice':
          uni.navigateTo({
            url: '/pages/chapterExercise/index?type=must'
          })
          break
        case 'theory-mock':
          uni.navigateTo({
            url: '/pages/examination/index?type=theory'
          })
          break
        default:
          console.log('Unknown card type:', type)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.study-card-grid {
  padding: 24rpx;
  background: transparent;
  .study-card {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    padding: 24rpx;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 16rpx;

    &:active {
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }
    
    .card-icon {
      width: 60rpx;
      height: 60rpx;
      margin-right: 16rpx;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        width: 100%;
        height: 100%;
      }

      // Emoji图标样式
      .icon-diamond,
      .icon-bell,
      .icon-tooth,
      .icon-edit {
        font-size: 40rpx;
        line-height: 1;
      }
    }

    .card-content {
      flex: 1;

      .card-title {
        display: block;
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 6rpx;
        line-height: 1.2;
      }

      .card-subtitle {
        display: block;
        font-size: 22rpx;
        color: #666;
        line-height: 1.2;
      }
    }

    // 橙色卡片 - 核心突破5000题
    &.card-orange {
      background: linear-gradient(135deg, #FF8A00 0%, #FFB366 100%);

      .card-title,
      .card-subtitle {
        color: #fff;
      }
    }

    // 红色卡片 - 能力提升
    &.card-red {
      background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);

      .card-title,
      .card-subtitle {
        color: #fff;
      }
    }

    // 蓝色卡片 - 必刷4000题
    &.card-blue {
      background: linear-gradient(135deg, #4ECDC4 0%, #7BDDD8 100%);

      .card-title,
      .card-subtitle {
        color: #fff;
      }
    }

    // 紫色卡片 - 理论阶段模考题
    &.card-purple {
      background: linear-gradient(135deg, #A855F7 0%, #C084FC 100%);

      .card-title,
      .card-subtitle {
        color: #fff;
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .study-card-grid {
    .study-card {
      .card-title {
        font-size: 26rpx;
      }

      .card-subtitle {
        font-size: 20rpx;
      }

      .card-icon {
        width: 50rpx;
        height: 50rpx;

        .icon-diamond,
        .icon-bell,
        .icon-tooth,
        .icon-edit {
          font-size: 36rpx;
        }
      }
    }
  }
}
</style>
