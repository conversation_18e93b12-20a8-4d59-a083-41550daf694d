<template>
  <view class="login" :style="style">
    <!-- 授权登陆 -->
    <button
      v-if="!isLogin()"
      open-type="getPhoneNumber"
      class="button"
      @getphonenumber="getphonenumber"
      @click="cbFn"
    ></button>
    <view class="slot" @click="success">
      <slot />
    </view>
  </view>
</template>

<script>
import store from '../../../../store/index.js'
import { isLogin } from '../../utils/index.js'
export default {
  props: {
    style: {},
    cb: {}
  },
  data() {
    return {
      // token: this.$store.state.jintiku.token
    }
  },
  computed: {
    token() {
      return this.$store.state.jintiku.token
    }
  },
  methods: {
    cbFn() {
      this.cb && this.cb()
    },
    getphonenumber(e) {
      // 默认登陆
      let code = "e.detail.code"
      if (!code) {
        this.$xh.Toast('请您授权手机号才可以操作哦！')
        return
      }
      this.$store
        .dispatch('jintiku/LOGIN', code)
        .then(() => {
          this.$store.dispatch('jintiku/SETMENUS').then(() => {
            this.$emit('success')
          })
        })
        .catch(err => {
          this.$emit('fail')
        })
    },
    success() {
      this.$emit('success')
    },
    isLogin
  }
}
</script>

<style lang="less" scoped>
.login {
  position: relative;

  .button {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: 100;
  }
}
</style>
