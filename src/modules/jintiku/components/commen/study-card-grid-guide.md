# StudyCardGrid 组件使用指南

## 🎯 快速开始

### 1. 在页面中引入组件

```vue
<template>
  <view>
    <study-card-grid @cardClick="handleCardClick" />
  </view>
</template>

<script>
import StudyCardGrid from '@/components/commen/study-card-grid.vue'

export default {
  components: {
    StudyCardGrid
  },
  methods: {
    handleCardClick(type) {
      console.log('点击了卡片:', type)
    }
  }
}
</script>
```

### 2. 卡片类型说明

| 类型 | 标识 | 名称 | 描述 | 跳转页面 |
|------|------|------|------|----------|
| 💎 | `core-breakthrough` | 核心突破5000题 | 冲刺必刷 快速提分 | `/pages/chapterExercise/index?type=core` |
| 🔔 | `ability-improvement` | 能力提升 | 阶段测试 | `/pages/intelligentEvaluation/index` |
| 🦷 | `must-practice` | 必刷4000题 | 重难点总结 | `/pages/chapterExercise/index?type=must` |
| 📝 | `theory-mock` | 理论阶段模考题 | 理论强化 | `/pages/examination/index?type=theory` |

## 🎨 自定义样式

### 修改卡片颜色

```scss
// 在你的页面样式中覆盖
.study-card-grid {
  .study-card {
    &.card-orange {
      background: linear-gradient(135deg, #your-color 0%, #your-color 100%);
    }
  }
}
```

### 修改卡片尺寸

```javascript
// 在组件的 data 中修改
data() {
  return {
    cardStyle: {
      padding: '0',
      height: '160rpx' // 修改高度
    }
  }
}
```

## 🔧 高级用法

### 1. 禁用自动跳转

如果你想完全自定义点击行为，可以修改组件：

```javascript
handleCardClick(type) {
  this.$emit('cardClick', type)
  // 注释掉自动跳转逻辑
  // switch (type) { ... }
}
```

### 2. 添加统计埋点

```javascript
handleCardClick(type) {
  // 添加统计埋点
  this.$store.dispatch('analytics/trackEvent', {
    event: 'study_card_click',
    properties: {
      card_type: type,
      timestamp: Date.now()
    }
  })
  
  console.log('点击了卡片:', type)
}
```

### 3. 动态控制卡片显示

```vue
<template>
  <study-card-grid 
    v-if="showStudyCards"
    @cardClick="handleCardClick" 
  />
</template>

<script>
export default {
  data() {
    return {
      showStudyCards: true
    }
  },
  computed: {
    showStudyCards() {
      // 根据用户权限或其他条件控制显示
      return this.$store.state.user.hasPermission
    }
  }
}
</script>
```

## 🐛 常见问题

### Q: 组件不显示怎么办？
A: 检查以下几点：
1. 确保已安装 uView UI
2. 确保在 main.js 中正确引入了 uView UI
3. 检查组件路径是否正确

### Q: 点击没有反应？
A: 检查：
1. 是否正确监听了 `@cardClick` 事件
2. 控制台是否有错误信息
3. 目标页面路径是否存在

### Q: 样式显示异常？
A: 可能的原因：
1. CSS 样式被其他样式覆盖
2. rpx 单位在某些平台显示异常
3. 渐变色在某些设备不支持

## 📱 平台兼容性

| 平台 | 支持情况 | 备注 |
|------|----------|------|
| 微信小程序 | ✅ | 完全支持 |
| H5 | ✅ | 完全支持 |
| App | ✅ | 完全支持 |
| 支付宝小程序 | ✅ | 需要测试emoji显示 |
| 百度小程序 | ✅ | 需要测试emoji显示 |

## 🔄 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 基于 uView UI Grid 组件
- 支持 4 个学习模块卡片
- 支持点击事件和自动跳转
- 响应式设计

## 📞 技术支持

如果在使用过程中遇到问题，请：
1. 查看控制台错误信息
2. 检查组件文档和示例
3. 联系开发团队获取支持
