<template>
  <view class="practice-card" @click="handleClick">
    <view class="card-icon">
      <image :src="icon" mode="aspectFit" />
    </view>
    <view class="card-content">
      <text class="card-title">{{ title }}</text>
      <text class="card-subtitle">{{ subtitle }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'PracticeCard',
  props: {
    icon: {
      type: String,
      required: true
    },
    title: {
      type: String,
      required: true
    },
    subtitle: {
      type: String,
      default: ''
    },
    url: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleClick() {
      this.$emit('click', {
        title: this.title,
        url: this.url
      })
      
      if (this.url) {
        uni.navigateTo({
          url: this.url
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.practice-card {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 16rpx;
  
  .card-icon {
    width: 80rpx;
    height: 80rpx;
    margin-right: 24rpx;
    
    image {
      width: 100%;
      height: 100%;
    }
  }
  
  .card-content {
    flex: 1;
    
    .card-title {
      display: block;
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 8rpx;
    }
    
    .card-subtitle {
      display: block;
      font-size: 24rpx;
      color: #999;
    }
  }
  
  &:active {
    background: #f8f8f8;
  }
}
</style>
