import getHttp, { newRequest } from '@utlis/request'
import Base64 from '../../../utlis/base64'
import { noToastUrl, noCheckoutLogin, visitorMode } from './httpRequestConfig'
import store from '../store/index'
import $xh from '../../../utlis/index'
import { goToLogin, goToMajor } from '../utils/index'
// import thisNewRequest from './request'
import { shelf_platform_id } from '../config'
let userinfo = uni.getStorageSync('__xingyun_userinfo__') || {}
// 设置标识id
let cacheIdentId = {
  // id缓存
}
uni.$on('merch_clear', () => {
  console.log('清楚')
  userinfo = {}
})
function setIdentify(header = {}) {
  let path = $xh.getPath()
  let id = ''
  if (cacheIdentId[path]) {
    id = cacheIdentId[path]
  } else {
    store.state.menus &&
      store.state.menus.forEach(item => {
        if ('modules/jintiku/' + item.link == path) {
          id = item.id
          // 设置缓存
          cacheIdentId[path] = id
        }
      })
  }
  if (id) {
    let baseencode = Base64.encode(id)
    header['x-menu-identy'] = baseencode
  } else {
    let baseencode = Base64.encode('0')
    header['x-menu-identy'] = baseencode
  }
}
function setPlatForm(header = {}) {
  header['x-platform-id'] = Base64.encode(shelf_platform_id)
}
function setLoginInfo(header, data) {
  // 在header上加上相关东西
  header['x-token'] = store.state.token
  // 设置标识
  setIdentify(header)
  // 设置平台
  setPlatForm(header)
  // 往data上增加默认数据
  let majorInfo = uni.getStorageSync('__xingyun_major__') || {}

  userinfo = uni.getStorageSync('__xingyun_userinfo__') || {}

  if (
    majorInfo &&
    majorInfo.major_id &&
    !Object.keys(data).includes('professional_id')
  ) {
    data.professional_id = majorInfo.major_id
  }
  if (
    userinfo &&
    userinfo.student_id &&
    !Object.keys(data).includes('user_id')
  ) {
    data.user_id = userinfo.student_id
    data.student_id = userinfo.student_id
    data.merchant_id = process.env.VUE_APP_MERCHANTID
    data.brand_id = process.env.VUE_APP_BRANDID
  }
}
export default newRequest(
  (function () {
    if (process.env.NODE_ENV == 'development') {
      // return 'https://xypaytest.jinyingjie.com/api'
      //#ifdef MP-WEIXIN
      return 'https://xingyundev.jinyingjie.com/api'
      //#endif
      //#ifdef H5
      return '/api'
      //#endif
    }
    return process.env.VUE_APP_BASE_API
  })()
)({
  noToastUrl,
  noCheckoutLogin,
  normal_code: 100000, // 正常码
  handlerStatusCode: {
    // 特殊装态吗处理
    100002: function (data, request) {
      // $xh.Toast('登陆过期，请重新登录')
      store.state.token = ''
      // uni.clearStorageSync()
      uni.removeStorage('__xingyun_userinfo__')
      uni.removeStorage('__xingyun_token__')
      goToLogin('handlerStatusCode')
    }
  },
  checkLogin(header = {}, url, data = {}) {
    if (!userinfo.token) {
      userinfo = uni.getStorageSync('__xingyun_userinfo__') || {}
    }

    header['x-merchant-id'] = Base64.encode(process.env.VUE_APP_MERCHANTID)
    header['x-brand-id'] = Base64.encode(process.env.VUE_APP_BRANDID)
    if (visitorMode.includes(url)) {
      setLoginInfo(header, data)
      return true
    }
    // 校验是否登录
    if (store.state.token) {
      // 在header上加上相关东西
      header['x-token'] = store.state.token
      // 设置标识
      setIdentify(header)
      // 设置平台
      setPlatForm(header)
      // 往data上增加默认数据
      let majorInfo = uni.getStorageSync('__xingyun_major__') || {}

      userinfo = uni.getStorageSync('__xingyun_userinfo__') || {}

      if (
        majorInfo &&
        majorInfo.major_id &&
        !Object.keys(data).includes('professional_id')
      ) {
        data.professional_id = majorInfo.major_id
      }
      if (!majorInfo.major_id) {
        // data.professional_id = '123456789'
        // $xh.push('jintiku', `pages/major/index`)
        // goToMajor()
      }
      if (
        userinfo &&
        userinfo.student_id &&
        !Object.keys(data).includes('user_id')
      ) {
        data.user_id = userinfo.student_id
        data.student_id = userinfo.student_id
      }
      return true
    } else {
      store.state.token = ''
      uni.setStorageSync('__xingyun_token__', '')
      // goToLogin('checkLogin')
      return false
    }
  }
})
