import $xh from '../../../utlis/index'
import {
  getCode,
  Login,
  smslogin,
  getPhone,
  getMenus,
  shareRecord,
  getToken,
  activateuserRecord,
  changeBasic,
  activityRecord,
  activityRaram
} from '../api/index'
import { getExamTime, setTimeInfo } from '../api/userInfo'
import { app_id, shelf_platform_id } from '../config'
import { filterUsabled, filterIsMenu } from '../utils/index'
import { version } from '../config'

export default {
  state: {
    token: uni.getStorageSync('__xingyun_token__') || '',
    userinfo: uni.getStorageSync('__xingyun_userinfo__') || {},
    majorInfo: uni.getStorageSync('__xingyun_major__') || {},
    menus: uni.getStorageSync('__xingyun_menus__') || [],
    routes: $xh.getStorage('__xingyun_routes__') || [],
    employee_id: uni.getStorageSync('__promotion_employee_id__') || '',
    xyppid: uni.getStorageSync('__xingyun_xyppid__') || ''
    // merchantInfo: uni.getStorageSync('__merchant_info__') || {
    //   brand_id: '',
    //   merchant_id: ''
    // }
  },
  mutations: {
    setState(store) {
      store.token = ''
      store.userinfo = {}
      store.majorInfo = {}
      store.menus = []
      store.routes = []
      uni.removeStorageSync('__xingyun_token__', '')
      uni.removeStorageSync('__xingyun_userinfo__')
    },
    setToken(store, token) {
      store.token = token
      uni.removeStorageSync('__xingyun_token__')
      uni.setStorageSync('__xingyun_token__', token)
    },
    removeToken(store) {
      store.token = ''
      uni.setStorageSync('__xingyun_token__', '')
    },
    setEmployeeId(store, employee_id) {
      if (String(employee_id).length > 10) {
        store.employee_id = employee_id
        uni.setStorageSync('__promotion_employee_id__', employee_id)
      }
    },
    setXyppid(store, xyppid) {
      store.xyppid = xyppid
      uni.setStorageSync('__xingyun_xyppid__', xyppid)
    },
    setUserinfo(store, userinfo) {
      store.userinfo = userinfo
      // let { major_id = '', major_name = '' } = userinfo
      // let obj = {
      //   major_id,
      //   major_name
      // }
      // store.userinfo = obj
      uni.removeStorageSync('__xingyun_userinfo__')
      uni.setStorageSync('__xingyun_userinfo__', userinfo)
      // uni.setStorageSync('__xingyun_major__', obj)
    },
    setMenus(store, menus) {
      // 设置菜单
      store.menus = $xh.flat(menus)
      uni.setStorageSync('__xingyun_menus__', store.menus)
      let routes = filterIsMenu(store.menus)
      routes = filterUsabled(routes)
      store.routes = routes.map(item => {
        return item.link
      })
      uni.setStorageSync('__xingyun_routes__', store.routes)
    }
  },
  actions: {
    UNlOGIN({ commit }) {
      commit('setState')
    },
    LOGIN({ commit, dispatch, state }, code) {
      return new Promise((resolve, reject) => {
        wx.login({
          success(e) {
            let login_code = e.code
            let { major_id = '' } = uni.getStorageSync('__xingyun_major__')
            getCode({
              app_id,
              code: login_code,
              major_id
            })
              .then(data => {
                // $xh.setStorageSync_('__xingyun_weixinInfo__', data.data)
                uni.setStorageSync('__xingyun_weixinInfo__', data.data)
                getPhone({
                  app_id,
                  code
                })
                  .then(response => {
                    let phone = response.data.phone

                    uni.setStorageSync('__xingyun_userPhone__', phone)
                    let objInfo = {
                      merchant_id: process.env.VUE_APP_MERCHANTID,
                      brand_id: process.env.VUE_APP_BRANDID,
                      channel_id: process.env.VUE_APP_CHANNELID,
                      extendu_id:
                        state.employee_id || process.env.VUE_APP_EXTENDUID,
                      wxopenid: data.data.openid,
                      account: phone,
                      activity_id: '',
                      need_employee_info: 1,
                      // brand_id: process.env.VUE_APP_BRANDID,
                      // merchant_id: process.env.VUE_APP_MERCHANTID,
                      app_id
                    }
                    let shareInfo = uni.getStorageSync('__xingyun_share__')
                    if (shareInfo.employee_id) {
                      objInfo.employee_id = shareInfo.employee_id
                      objInfo.promoter_type = shareInfo.promoter_type
                    }
                    Login(objInfo)
                      .then(res => {
                        let token = res.data.token
                        let info = res.data
                        //没有昵称设置默认
                        let nickname = info.nickname
                        if (!nickname) {
                          info.nickname = '金题考神' + phone.substring(7)
                        }
                        commit('setUserinfo', info)
                        commit('setToken', token)

                        if (!nickname) {
                          changeBasic({
                            id: info.student_id,
                            nickname: info.nickname,
                            avatar: info.avatar
                          })
                        }
                        uni.removeStorageSync('__anwers_list__')
                        if (state.employee_id) {
                          activateuserRecord({
                            activity_id: '563493964766776102',
                            inviter_id: state.employee_id,
                            inviter_type: 1
                          })
                        }

                        if (shareInfo.employee_id) {
                          shareRecord({
                            ...shareInfo,
                            student_id: info.student_id,
                            promoter_id: shareInfo.student_id,
                            brand_id: process.env.VUE_APP_BRANDID,
                            merchant_id: process.env.VUE_APP_MERCHANTID,
                            channel_id: process.env.VUE_APP_CHANNELID,
                            extendu_id: process.env.VUE_APP_EXTENDUID,
                            type: '1'
                          })
                        }

                        dispatch('ACTIVITY_RECORD')
                        resolve(info)
                        // 获取真正的token
                        // getToken({
                        //   merchant_id: process.env.VUE_APP_MERCHANTID,
                        //   brand_id: process.env.VUE_APP_BRANDID
                        // }).then(result => {
                        //   commit('setUserinfo', result.data)
                        //   commit('setToken', result.data.token)
                        //   uni.setStorageSync(
                        //     '__tiku_version_code',
                        //     version || ''
                        //   )
                        //   resolve(info)
                        // })
                      })
                      .catch(err => {
                        reject(err)
                      })
                  })
                  .catch(err => {
                    reject(err)
                  })
              })
              .catch(err => {
                reject(err)
              })
          }
        })
      })
    },
    CODELOGIN({ commit }, objInfo) {
      return new Promise((resolve, reject) => {
        smslogin(objInfo)
          .then(res => {
            let token = res.data.token
            let info = res.data
            let userinfo = uni.getStorageSync('__xingyun_userinfo__') || {}
            commit('setUserinfo', {
              ...info,
              ...userinfo
            })
            commit('setToken', token)
            resolve()
            // 获取真正的token
            // getToken({
            //   merchant_id: info.merchant[0].merchant_id,
            //   brand_id: info.merchant[0].brand_id
            // }).then(result => {
            //   commit('setToken', result.data.token)
            //   resolve(info)
            // })
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    SETMENUS({ commit }) {
      return new Promise((resolve, reject) => {
        getMenus({
          platform_id: shelf_platform_id
        })
          .then(menuData => {
            commit('setMenus', menuData.data)
            resolve()
          })
          .catch(reject)
      })
    },
    ACTIVITY_RECORD({ commit, state }) {
      let params = {
        xyppid: state.xyppid,
        phone: uni.getStorageSync('__xingyun_userPhone__')
      }
      return new Promise((resolve, reject) => {
        console.log('活动记录参数', params)
        if (params.xyppid && params.phone) {
          activityRecord(params)
            .then(res => {
              console.log('活动记录结果-success', res)
              resolve(res)
            })
            .catch(err => {
              console.log('活动记录结果-error', err)
              reject(err)
            })
        } else {
          reject('err')
        }
      })
    }
  },
  getters: {
    brand_id(state) {
      if (state.userinfo.merchant && state.userinfo.merchant.length) {
        return state.userinfo.merchant[0].brand_id
      }
      return ''
    },
    merchant_id(state) {
      if (state.userinfo.merchant && state.userinfo.merchant.length) {
        return state.userinfo.merchant[0].merchant_id
      }
      return ''
    }
  }
}
