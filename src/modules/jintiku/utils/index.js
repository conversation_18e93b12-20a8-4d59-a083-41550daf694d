import store from '../store/index'
import $xh from '../../../utlis/index'
import Base64 from '../../../utlis/base64'
import { miniappAuth } from '../api/index.js'
import { getLearningData } from '../api/userInfo'
import { version, requestSubscribeMessage, app_id } from '../config'
export function isLogin() {
  return !!store.state.token
}
let isRouting = false // 是否在跳转中
export function goToLogin(type) {
  if (isRouting) {
    return
  }
  isRouting = true
  setTimeout(() => {
    isRouting = false
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage.route
    if (currentRoute == 'modules/jintiku/pages/loginCenter/index') {
      return
    }
    console.log(type)
    $xh.push('jintiku', 'pages/loginCenter/index')
  }, 200)
}

let isGoMajorIng = false
export function goToMajor() {
  if (isGoMajorIng) {
    return
  }
  isGoMajorIng = true

  setTimeout(() => {
    isGoMajorIng = false

    let { major_id = '', major_name = '' } =
      uni.getStorageSync('__xingyun_major__')
    console.log(
      uni.getStorageSync('__xingyun_major__'),
      "uni.getStorageSync('__xingyun_major__')"
    )
    console.log(
      major_id,
      major_name,
      uni.getStorageSync('__xingyun_major__'),
      '__xingyun_major__'
    )
    if (major_id && major_name) {
      return
    }
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    const currentRoute = currentPage.route
    if (currentRoute == 'modules/jintiku/pages/major/index') {
      return
    }
    console.log('tiaozhuan888')
    $xh.push('jintiku', 'pages/major/index')
  }, 200)
}

// 遍历可用的菜单数据
export function filterUsabled(arr) {
  return arr.filter(item => {
    return item.is_shelf == '1'
  })
}
// 遍历菜单类型的数据
export function filterIsMenu(arr) {
  return arr.filter(item => {
    return item.type == '1'
  })
}
// 发起支付
export function pay(data, success, fail) {
  uni.getProvider({
    service: 'payment',
    success(e) {
      const { provider } = e
      uni.requestPayment({
        provider,
        ...data,
        success(e) {
          success && success(e)
        },
        fail(err) {
          fail && fail(err)
        }
      })
    }
  })
}
/**
 * @转换时间
 * @time number类型
 * @return 返回值为: xx:xx:xx
 */
export function transformTime(time) {
  // 切换小时
  let hour = parseInt(time / 60 / 60)
    .toString()
    .padStart(2, 0)
  let min = parseInt((time % 3600) / 60)
    .toString()
    .padStart(2, 0)
  let second = parseInt(time % 60)
    .toString()
    .padStart(2, 0)
  return `${hour}:${min}:${second}`
}

// 转换题库
function transform(question_list) {
  let _question_list = []
  for (let i = 0; i < question_list.length; i++) {
    if (question_list[i].stem_list.length == 1) {
      // _question_list.push(question_list[i])
      _question_list.push({
        ...question_list[i],
        question_id: question_list[i].id
      })
    } else {
      for (let j = 0; j < question_list[i].stem_list.length; j++) {
        let _stem_list = question_list[i].stem_list[j]
        let question_id = question_list[i].id
        let res = {
          ...question_list[i],
          stem_list: [_stem_list],
          question_id
        }
        _question_list.push(res)
      }
    }
  }
  return _question_list
}
function replaceTextSymbolsInHtml(htmlString) {
  // 使用正则表达式匹配大于号和小于号，确保它们左右是非字母字符或字符串边界
  // 注意：这个正则表达式不会处理所有复杂情况，比如属性值中的特殊字符或文本内容中的引号等
  // 它只是一个简化的示例，用于演示如何根据上下文替换字符
  return htmlString.replace(
    /(^|[^a-zA-Z])([<>])([^a-zA-Z]|$)/g,
    (match, p1, p2, p3) => {
      // p1 是尖角号左边的字符（或字符串开始位置）
      // p2 是尖角号本身（< 或 >）
      // p3 是尖角号右边的字符（或字符串结束位置）
      // 根据 p2 的值替换为相应的中文符号
      return p1 + (p2 === '<' ? '＜' : '＞') + p3
    }
  )
}

export function setQuestionLists(question_list) {
  let _question_list = transform(question_list)
  return _question_list.map(item => {
    return {
      ...item,
      thematic_stem: item.thematic_stem
        .replace(/style=""/gi, '')
        .replace(
          /<img([\s\w"-=/.:;]+)>/gi,
          "<img style='max-width: 100%;' $1>"
        ),
      sub_question_id: item.stem_list[0].id,
      doubt: false, // 是否为疑问题
      lookAnswer: false, // 是否查看答案了
      stem_list: item.stem_list.map(res => {
        let option = res.option
        let answer = res.answer
        if (option && typeof option == 'string') {
          try {
            option = JSON.parse(option)
          } catch (error) {
            console.log(error, '?????')
            option = []
          }
        } else {
          option = []
        }
        if (typeof answer == 'string') {
          try {
            answer = JSON.parse(answer)
          } catch (error) {
            answer = []
          }
        }
        let resource_info = {}
        try {
          resource_info = JSON.parse(item.resource_info)
        } catch (error) {}
        if (item.type == '14') {
          option = ['已掌握', '未掌握']
          answer = ['0']
        }
        return {
          ...res,
          option: option
            ? option.map(item => {
                if (typeof item != 'string') {
                  return item
                }
                if (item.includes('<') || item.includes('>')) {
                  try {
                    return item
                      .replace(/\s+<\s+/g, '＜')
                      .replace(/\s+>\s+/g, '＞')
                      .replace(/\d+>\d+/g, '＞')
                      .replace(/\d+<\d+/g, '＜')
                      .replace(/[\u4e00-\u9fff]+<\d+/g, '＜')
                      .replace(/[\u4e00-\u9fff]+<\s+/g, '＜')
                      .replace(/[\u4e00-\u9fff]+>\d+/g, '＞')
                      .replace(/[\u4e00-\u9fff]+>\s+/g, '＞')
                    // .replace(/[^a-zA-Z]+>[^a-zA-Z]+/g, '＞')
                    // .replace(/[^a-zA-Z]+<[^a-zA-Z]+/g, '＞')
                  } catch (error) {
                    return item
                  }
                  // return replaceTextSymbolsInHtml(item)
                }
                return item
              })
            : [],
          answer: answer ? answer.map(item => item * 1) : [],
          selected: (function () {
            if (isFillBlanks()) {
              return res.selected ? res.selected : option.map(() => '')
            }
            return res.selected ? res.selected : []
          })(), // 用户选择选项
          multiple: answer?.length > 1 ? true : false, // 是否是多选
          parse: item.parse
            ? item.parse
                .replace(/style=""/gi, '')
                .replace(
                  /<img([\s\w"-=/.:;]+)>/gi,
                  "<img style='max-width: 100%;' $1>"
                )
            : '暂无解析',
          knowledge_ids_name: item.knowledge_ids_name
            ? item.knowledge_ids_name
            : [],
          resource_info: resource_info
          // resource_info: {
          //   // 点信息
          //   data: [
          //     {
          //       coord: '0.184,0.07108799306458605,0.176,0.3070275249241439', // 宽 高 left top
          //       file: 'https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/2minute-demo.mp4' // 音频
          //     },
          //     {
          //       coord:
          //         '0.10666666666666667,0.03814477676636324,0.384,0.33634048547897705', // 宽 高 left top
          //       file: 'https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/2minute-demo.mp4' // 音频
          //     }
          //   ],
          //   img: {
          //     path: 'https://jinyingjiedev.oss-cn-beijing.aliyuncs.com/385164383640140890/2024/01/31/1706672212732da9c-1706672212732-91730.jpg', // 图片地址
          //     width_height: '375,576.75' // 宽高
          //   },
          //   video: {
          //     path: 'https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/2minute-demo.mp4' // 音频地址
          //   }
          // }
        }
      })
    }
  })
}
// 交卷反向回滚数据
export function setSubmitPageData(_data) {
  let data = JSON.parse(JSON.stringify(_data))
  // 交卷
  let obj = {}
  data.forEach(item => {
    if (!obj[item.question_id]) {
      obj[item.question_id] = item
      return
    }
    obj[item.question_id].stem_list.push(item.stem_list[0])
  })
  let arr = []
  for (let key in obj) {
    arr.push(obj[key])
  }
  let question_info = arr.map(item => {
    return {
      question_id: item.question_id,
      cost_time: item.cost_time ? item.cost_time : 1,
      user_option: item.stem_list.map(res => {
        return {
          sub_question_id: res.id,
          answer: res.selected.map(s => s + '')
        }
      })
    }
  })
  return question_info
}
export function rollBACKPageData(_data) {
  let data = JSON.parse(JSON.stringify(_data))
  // 交卷
  let obj = {}
  data.forEach(item => {
    if (!obj[item.question_id]) {
      obj[item.question_id] = item
      return
    }
    obj[item.question_id].stem_list.push(item.stem_list[0])
  })
  let arr = []
  for (let key in obj) {
    arr.push(obj[key])
  }
  let question_info = arr.map(item => {
    return {
      question_id: item.question_id, // 题目id
      user_option: item.stem_list.map(res => {
        return {
          sub_question_id: res.id,
          answer: res.selected.map(s => s + ''), // 用户的答案
          ...res
        }
      })
    }
  })
  return question_info
}
// 错题比对
export function diffAnswer(arr, arrOrigin) {
  let _arr = JSON.stringify(arr.sort().map(item => item + ''))
  let _arrOrigin = JSON.stringify(arrOrigin.sort().map(item => item + ''))
  return _arr == _arrOrigin
}
export const questionHelper = {
  diffAnswer(question) {
    // 获取用户是否做对了
    let _question = JSON.parse(JSON.stringify(question))
    return diffAnswer(
      _question.stem_list[0].selected,
      _question.stem_list[0].answer
    )
  },
  getSelected(question) {
    // 获取当前题的用户选择
    return question.stem_list[0].selected
  },
  isSelected(question) {
    // 用户是否选择了
    return question.stem_list[0].selected.length > 0
  },
  getAnswer(question) {
    // 获取当前题的答案
    return question.stem_list[0].answer
  },
  // 当前题是否是多选
  isMultiple(question) {
    return question.stem_list[0].multiple
  }
}
// 转换专业ID
export function transformDataId(arr) {
  return arr
  return arr.map(item => {
    if (item.subs && item.subs.length) {
      item.subs = transformDataId(item.subs)
    }
    return {
      ...item,
      id: item.data_id,
      _id: item.id
    }
  })
}
// 是否是简答题
export function isSubjective(type) {
  return type == '8' || type == '10' // 14是案例题
}
// 是否是填空题
export function isFillBlanks(type) {
  // TODO
  return type == '9'
}
// 是否是普通病例题
export function isOrdinaryCases(type) {
  return type == '3' || type == '4' || type == '6' || type == '17'
}
export function isB1(type) {
  return type == '5' || type == '16' // 16:B
}
// 是否是选择题
export function isSelectedType(type) {
  return !isFillBlanks(type) && !isSubjective(type)
}
// 是否是单选题类型 包含a1 a2 单选
export function isSingle(type) {
  return type == '1' || type == '2' || type == '12' || type == '15'
}
// 是否是多选题
export function isMultiple(type) {
  return type == '7' || type == '13'
}
// 是否是是非题
export function isRightWrong(type) {
  return type == '11'
}
export function getAudioAll(html) {
  try {
    const regex = /<audio\s*src="([^"]*)"[^>]*>/g
    const matches = []
    let match
    while ((match = regex.exec(html)) !== null) {
      matches.push(match[1])
    }
    return matches
  } catch (error) {
    console.log(error)
    return []
  }
}

/**
 * @desc 函数节流
 * @param func 函数
 * @param wait 延迟执行毫秒数
 * @param type 1 表时间戳版，2 表定时器版
 */
export const throttle = (func, wait = 300, type = 1) => {
  if (type === 1) {
    var previous = 0
  } else if (type === 2) {
    var timeout
  }
  return function () {
    let context = throttle
    let args = arguments
    if (type === 1) {
      let now = Date.now()
      if (now - previous > wait) {
        func.apply(context, args)
        previous = now
      }
    } else if (type === 2) {
      if (!timeout) {
        timeout = setTimeout(() => {
          timeout = null
          func.apply(context, args)
        }, wait)
      }
    }
  }
}

export const isVersionUpdata = func => {
  let key = '__tiku_version_code'
  let currentCode = uni.getStorageSync(key)
  let token = uni.getStorageSync('__xingyun_token__')
  if (currentCode != version && token) {
    setTimeout(() => {
      func()
      uni.setStorageSync(key, version)
    }, 0)
    return true
  } else {
    uni.setStorageSync(key, version)
    return false
  }
}
export const getNextChapter = knowledge_id => {
  let lastList = uni.getStorageSync('__chapterLastList')
  let currentIndex = lastList.findIndex(e => e.id == knowledge_id)
  if (currentIndex == lastList.length - 1) {
    $xh.Toast('已经是最后一节了！')
  } else {
    let data = lastList[currentIndex + 1]
    uni.showModal({
      title: '温馨提示',
      content: `下一节：${data.sectionname}！`,
      cancelText: '再看看',
      confirmText: '确认进入',
      success: res => {
        if (res.confirm) {
          if (data.not_answered_question_num == '0') {
            uni.redirectTo({
              url: `/modules/jintiku/pages/makeQuestion/lookAnalysisQuestion?knowledge_id=${data.id}&type=10&chapter_id=${data.sectionprent}&teaching_system_package_id=${data.teaching_system_package_id}`,
              fail: err => {
                console.log(err)
              }
            })
          } else {
            uni.redirectTo({
              url: `/modules/jintiku/pages/makeQuestion/makeQuestion?knowledge_id=${data.id}&type=10&chapter_id=${data.sectionprent}&teaching_system_package_id=${data.teaching_system_package_id}`,
              fail: err => {
                console.log(err)
              }
            })
          }
        } else if (res.cancel) {
        }
      }
    })
  }
}
export const setSubscribeMessage = type => {
  function fail(err) {
    console.log(err, 'setSubscribeMessage-fail')
  }
  function success(res) {
    let openid = uni.getStorageSync('__xingyun_weixinInfo__').openid
    miniappAuth({
      app_id: app_id,
      app_open_id: openid,
      student_id: uni.getStorageSync('__xingyun_userinfo__').student_id
    })
    console.log(res, 'setSubscribeMessage-success')
  }
  if (type == 'login') {
    wx.requestSubscribeMessage({
      tmplIds: [requestSubscribeMessage.login],
      success,
      fail
    })
  }
  if (type == 'examination') {
    wx.requestSubscribeMessage({
      tmplIds: [requestSubscribeMessage.examination],
      success,
      fail
    })
  }
  if (type == 'goods') {
    wx.requestSubscribeMessage({
      tmplIds: [requestSubscribeMessage.goods],
      success,
      fail
    })
  }
  if (type == 'share') {
    wx.requestSubscribeMessage({
      tmplIds: [requestSubscribeMessage.share],
      success,
      fail
    })
  }
}

// 文件上传
export const upLoad = tempfileurl => {
  return new Promise((resolve, reject) => {
    let formData = {}
    function success(uploadFileRes) {
      if (!uploadFileRes) {
        return reject({
          status: false,
          code_msg: '啥都没有返回！'
        })
      }
      console.log(uploadFileRes)
      let data = JSON.parse(uploadFileRes.data)
      return resolve(data.data[0])
    }
    function fail(err) {
      return reject(err)
    }
    let baseUrl = process.env.VUE_APP_BASE_API
    // 认证变量
    const biseKey = process.env.VUE_APP_BASICKEY
    const biseValue = process.env.VUE_APP_BASICVALUE
    let base64 = Base64.encode(`${biseKey}:${biseValue}`)
    uni.uploadFile({
      header: {
        Authorization: 'Basic ' + base64
      },
      url: baseUrl + '/c/base/uploadfiles',
      filePath: tempfileurl,
      name: 'file',
      formData,
      success: success,
      fail: fail
    })
  })
}

export const shareAppMessage2 = func => {
  let { major_id = '', major_name = '' } =
    uni.getStorageSync('__xingyun_major__')
  let userinfo = uni.getStorageSync('__xingyun_userinfo__')
  let employee_id = ``
  if (userinfo?.employee_info?.employee_id) {
    employee_id = `&employee_id=${userinfo?.employee_info?.employee_id}`
  }
  const promise = new Promise(resolve => {
    getLearningData({}).then(data => {
      let res = data.data
      if (typeof func == 'function') {
        func()
      }
      resolve({
        title: `我在金题库已经刷了${res.total_num}题，累计学习${res.learn_time}小时，邀请你一起刷题！`,
        path: `/modules/jintiku/pages/index/index?major_id=${major_id}&major_name=${major_name}${employee_id}`
      })
    })
  })
  return {
    title: '金题库助你医路通关',
    path: `/modules/jintiku/pages/index/index?major_id=${major_id}&major_name=${major_name}${employee_id}`,
    promise
  }
}
