{"name": "金题库", "appid": "wxa8ca85794337e175", "versionCode": "101", "description": "金题库", "versionName": "1.0.0", "transformPx": false, "app-plus": {"compilerVersion": 3, "background": "#ededed", "usingComponents": true, "compatible": {"ignoreVersion": true}, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "safearea": {"background": "#CCCCCC", "bottom": {"offset": "auto"}}, "modules": {"Share": {}, "Contacts": {}}, "distribute": {"android": {"permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"]}, "ios": {"capabilities": {"entitlements": {"com.apple.developer.associated-domains": ["applinks:static-41ed5048-0dfe-458f-8f8d-15a663861356.bspapp.com"]}}}, "sdkConfigs": {"ad": {}, "share": {"weixin": {"appid": "wx527bf78f4101add4", "UniversalLinks": "https://static-41ed5048-0dfe-458f-8f8d-15a663861356.bspapp.com/uni-universallinks/__UNI__459A697"}}, "push": {"unipush": {}}, "speech": {"ifly": {}}}, "splashscreen": {"androidStyle": "default", "iosStyle": "common", "android": {"hdpi": "src/static/App/mlqdy.png", "xhdpi": "src/static/App/mlqdy.png", "xxhdpi": "src/static/App/mlqdy.png"}}, "icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}}, "nativePlugins": {}, "nvueCompiler": "uni-app", "nvueLaunchMode": "", "uniStatistics": {"enable": true}}, "h5": {"publicPath": "/", "router": {"mode": "hash"}, "devServer": {"port": 9000, "proxy": {"/api": {"target": "https://xypaydev.jinyingjie.com/api", "pathRewrite": {"^/api": ""}}, "/test": {"target": "https://xingyuntest.jinyingjie.com/api", "pathRewrite": {"^/test": ""}}}}, "sdkConfigs": {"maps": {"qqmap": {"key": "W6MBZ-26366-YULSE-MZLGE-LVECH-OGFWD"}}}, "optimization": {"subPackages": true, "treeShaking": {"enable": true}}}, "quickapp": {}, "mp-weixin": {"appid": "wxa8ca85794337e175", "setting": {"urlCheck": false}, "usingComponents": true, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredPrivateInfos": ["getLocation"], "optimization": {"subPackages": true}, "lazyCodeLoading": "requiredComponents", "componentFramework": "glass-easel", "rendererOptions": {"skyline": {"disableABTest": true, "defaultDisplayBlock": true, "defaultContentBox": true, "tagNameStyleIsolation": "legacy", "enableScrollViewAutoSize": true}}}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true, "appid": "26388516"}, "mp-toutiao": {"usingComponents": true}, "mp-qq": {"usingComponents": true}, "_spaceID": "41ed5048-0dfe-458f-8f8d-15a663861356"}